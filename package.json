{"name": "digigo-auth-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate": "dotenv -e .env -- npx sequelize-cli --options-path ./.sequelizerc db:migrate", "make-migrations": "dotenv -e .env -- npx sequelize-cli --options-path ./.sequelizerc migration:generate --name"}, "dependencies": {"@nestjs/axios": "3.0.0", "@nestjs/common": "^10.2.7", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.2.7", "@nestjs/jwt": "^10.1.1", "@nestjs/microservices": "^10.2.7", "@nestjs/passport": "^10.0.2", "@nestjs/platform-fastify": "^10.2.7", "@nestjs/schedule": "^5.0.1", "@nestjs/sequelize": "^10.0.1", "@nestjs/swagger": "^7.1.13", "axios": "^1.5.1", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.3.1", "form-data": "^4.0.2", "handlebars": "^4.7.8", "html-pdf": "^3.0.1", "i18n-2": "^0.7.3", "mysql2": "^3.11.0", "nest-winston": "^1.9.4", "nestjs-redis": "^1.3.3", "nodemailer": "^6.9.16", "number-to-words": "^1.2.4", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "redis": "^4.6.13", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "sequelize": "^6.37.3", "sequelize-cli": "^6.6.2", "sequelize-typescript": "^2.1.6", "swagger-ui-express": "^5.0.0", "tedious": "^18.2.0", "uuid": "^10.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@nestjs/cli": "^10.1.18", "@nestjs/platform-express": "^10.4.15", "@nestjs/schematics": "^10.0.2", "@nestjs/testing": "^10.2.7", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.19", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.5", "@types/lodash": "^4.14.202", "@types/multer": "^1.4.12", "@types/node": "^20.8.6", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^3.0.10", "@types/passport-local": "^1.0.36", "@types/sequelize": "^4.28.16", "@types/supertest": "^2.0.14", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "aws-sdk": "^2.1692.0", "dotenv-cli": "^7.4.2", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "install": "^0.13.0", "jest": "^29.7.0", "multer": "^1.4.5-lts.1", "npm": "^11.0.0", "prettier": "^3.0.3", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3", "xlsx": "^0.18.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}