import { DataTypes, QueryInterface } from "sequelize";

export default {
	up: async (queryInterface: QueryInterface) => {
		await queryInterface.createTable("kpi", {
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			kpi_title: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			plan_type: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			plan_period: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			cycle_time_start: {
				type: DataTypes.DATE,
				allowNull: false,
			},
			cycle_time_end: {
				type: DataTypes.DATE,
				allowNull: false,
			},
			departments: {
				type: DataTypes.JSON,
				allowNull: true,
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: true,
				defaultValue: DataTypes.NOW,
			},
		});
	},

	down: async (queryInterface: QueryInterface) => {
		await queryInterface.dropTable("kpi");
	},
};
