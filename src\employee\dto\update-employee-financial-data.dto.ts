import {
    IsString,
    IsEmail,
    IsInt,
    Min,
    <PERSON>,
    Length,
    IsDate,
    IsArray,
    ArrayNotEmpty,
    ValidateNested,
    IsUrl,
    IsNotEmpty,   
    IsNumber, 
    IsMobilePhone, 
    IsOptional,
    IsObject,
    IsEnum
} from 'class-validator';
import { Type } from 'class-transformer';

import {
    GENDER,
    BL<PERSON>OD_GROUP,
    BANK_LIST
} from "../../constants/api.enums";
import { Json } from 'sequelize/types/utils';

export class updateEmployeeFinancialDataDto {
    @IsString()
    @IsOptional()
    tin_no: string;

    @IsString()
    @IsOptional()
    tin_certificate: string;

    @IsEnum(BANK_LIST, { message: 'bank_name must be a valid bank from the list' })
    @IsOptional()
    bank_name?: BANK_LIST ;

    @IsString()
    @IsOptional()
    bank_account: string; // db column is account_no
    
    @IsString()
    @IsOptional()
    account_no: string;

    @IsNotEmpty()
    profile_id: number;

    @IsNotEmpty()
    member_id: number;

    @IsNotEmpty()
    business_member_id: number;
}
