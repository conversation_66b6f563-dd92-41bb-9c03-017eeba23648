"use strict";

import { DataTypes } from "sequelize";
import { DataType } from "sequelize-typescript";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.createTable("employee_kpis", {
			id: {
				type: Sequelize.BIGINT,
				autoIncrement: true,
				primaryKey: true,
			},
			employeeId: {
				type: Sequelize.INTEGER.UNSIGNED,
				allowNull: false,
				references: {
					model: "business_member",
					key: "id",
				},
				onUpdate: "CASCADE",
				onDelete: "CASCADE",
			},
			designation: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			departmentId: {
				type: Sequelize.INTEGER.UNSIGNED,
				allowNull: false,
				references: {
					model: "business_departments",
					key: "id",
				},
				onUpdate: "CASCADE",
				onDelete: "CASCADE",
			},
			kpiId: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: "kpi",
					key: "id",
				},
				onUpdate: "CASCADE",
				onDelete: "CASCADE",
			},
			unit: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			lineManagerId: {
				type: Sequelize.INTEGER.UNSIGNED,
				allowNull: false,
				references: {
					model: "business_member",
					key: "id",
				},
				onUpdate: "CASCADE",
				onDelete: "CASCADE",
			},
			lineManagerName: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			lineManagerDesignation: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			kpiArray: {
				type: Sequelize.JSON,
				allowNull: true,
			},
			lineManagerComment: {
				type: Sequelize.STRING,
				allowNull: true,
			},
			attachment: {
				type: Sequelize.STRING,
				allowNull: true,
			},
			recommendationForPromotion: {
				type: Sequelize.BOOLEAN,
				allowNull: true,
			},
			adminComment: {
				type: Sequelize.STRING,
				allowNull: true,
			},
			positionStatus: {
				type: Sequelize.ENUM("kpi", "appraisal"),
				allowNull: false,
			},
			status: {
				type: Sequelize.ENUM("pending", "reject", "complete"),
				allowNull: false,
			},
			createdAt: {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
			},
			updatedAt: {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
			},
		});
	},

	down: async (queryInterface, Sequelize) => {
		await queryInterface.dropTable("employee_kpis");
	},
};
