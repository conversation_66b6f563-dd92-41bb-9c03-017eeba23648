import {
    IsInt,
    IsString,
    <PERSON>NotEmpty,
    IsOptional,
    IsObject  
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class updateEmployeeAdditionalDataDto {
    @IsInt()
    @IsNotEmpty()
    public readonly business_member_id: number;

    @IsNotEmpty()
    //@IsObject()
    additional_data: {
        field_id: number;
        additional_data_id: number;
        value: string;
    }[];
}
