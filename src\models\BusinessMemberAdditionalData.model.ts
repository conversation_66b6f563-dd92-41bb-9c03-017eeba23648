import { Model, Table, Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { BusinessMembersModel } from './BusinessMember.model';
import { BusinessMemberAdditionalFieldsModel } from './BusinessMemberAdditionalField.model';
@Table({
  tableName: 'business_member_additional_data',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class BusinessMemberAdditionalDataModel extends Model<BusinessMemberAdditionalDataModel> {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => BusinessMembersModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  business_member_id: number; // Primary key of business_member table

  @ForeignKey(() => BusinessMemberAdditionalFieldsModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  field_id: number; // Primary key of business_member_additional_fields table

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  value: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  created_by: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  created_by_name: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  updated_by: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  updated_by_name: string;
  
  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: false
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: true // maybe allowNull: true
  })
  updated_at: Date;

  // relation
    @BelongsTo(() => BusinessMembersModel)
    member: BusinessMembersModel;

    @BelongsTo(() => BusinessMemberAdditionalFieldsModel)
    additional_field: BusinessMemberAdditionalFieldsModel;
}