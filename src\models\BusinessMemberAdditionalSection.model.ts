import { Model, Table, Column, DataType, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { BusinessesModel } from './Business.model';
import { BusinessMemberAdditionalFieldsModel } from './BusinessMemberAdditionalField.model';
@Table({
  tableName: 'business_member_additional_sections',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class BusinessMemberAdditionalSectionsModel extends Model<BusinessMemberAdditionalSectionsModel> {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => BusinessesModel)
  @Column({
    // Primary key of businesses table
    type: DataType.INTEGER,
    allowNull: true,
  })
  business_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  label: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  created_by: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  created_by_name: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  updated_by: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  updated_by_name: string;

  @BelongsTo(() => BusinessesModel)
  business: BusinessesModel;

  @HasMany(() => BusinessMemberAdditionalFieldsModel) 
  addtional_fields: BusinessMemberAdditionalFieldsModel[];
}