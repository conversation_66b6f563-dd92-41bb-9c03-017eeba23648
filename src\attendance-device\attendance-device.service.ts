import { HttpStatus, Injectable } from "@nestjs/common";
import { <PERSON>ron } from "@nestjs/schedule";
import axios from "axios";
import "dotenv/config";
import FormData from "form-data";
import { Op } from "sequelize";
import { ErrorLog } from "src/config/winstonLog";
import {
  AttendanceDeviceEnrollmentModel,
  BusinessDepartmentsModel,
  BusinessMembersModel,
  BusinessRolesModel,
  MembersModel,
  ProfilesModel,
  UserAttendanceDeviceModel,
} from "src/models";
import { errorResponse, successResponse } from "utils/response";
import { AllocateUserToDeviceDto } from "./dto/allocate-user-to-device.dto";
import { CreateEmployeeToDeviceDto } from "./dto/create-user.dto";
import { EnrollmentStatusDto } from "./dto/enrollment-status.dto";
import { StartEnrollmentDto } from "./dto/start-enrollment.dto";
import {
  AttendanceLog,
  CheckInCheckOutBodyLog,
} from "./types/attendance-log.type";

@Injectable()
export class AttendanceDeviceService {
  private ATTENDANCE_DEVICE_URL = "https://api-inovace360.com/api/v1";
  private ATTENDANCE_DEVICE_API_TOKEN =
    "7e58-97b5-59e1-a941-cd70-67f0-b5a2-7c30-0960-6cb5-efba-02e4-1d8c-2f2a-90b8-6dc1";
  private ATTENDANCE_SERVICE_API_URL = "https://api-digigo.dev-sheba.xyz";

  // storing employee data to attendance device server
  async createEmployeeToDevice(
    createEmployeeToDeviceDto: CreateEmployeeToDeviceDto
  ) {
    try {
      const formData = new FormData();
      formData.append("identifier", createEmployeeToDeviceDto.identifier);
      formData.append("name", createEmployeeToDeviceDto.name);
      formData.append(
        "primary_display_text",
        createEmployeeToDeviceDto.primary_display_text
      );
      formData.append(
        "secondary_display_text",
        createEmployeeToDeviceDto.secondary_display_text
      );
      formData.append("rfid", createEmployeeToDeviceDto.rfid);

      // if (createEmployeeToDeviceDto.image_base64) {
      //   formData.append("image_base64", createEmployeeToDeviceDto.image_base64);
      // }

      if (createEmployeeToDeviceDto.image) {
        formData.append("image", createEmployeeToDeviceDto.image, {
          filename: "1111image.png",
          contentType:
            createEmployeeToDeviceDto?.response.headers["content-type"],
        });
      }

      const response = await axios.post(
        `${this.ATTENDANCE_DEVICE_URL}/people`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },

          params: {
            api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
          },
        }
      );

      if (response.status !== 200) {
        return errorResponse(
          "Failed to create employee for attendance Device",
          response.data || {},
          response.status || HttpStatus.INTERNAL_SERVER_ERROR
        );
      }
      console.log(response.data);

      return successResponse(
        response.data,
        "Employee created successfully",
        HttpStatus.CREATED
      );
    } catch (error) {
      // console.log(error);
      ErrorLog(
        "employee create for attendance device",
        "attendance-device",
        error
      );
      return errorResponse(
        error?.message || "Failed to create employee for attendance Device",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getAttendanceDevices() {
    try {
      const response = await axios.get(
        `${this.ATTENDANCE_DEVICE_URL}/devices`,
        {
          params: {
            api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
          },
        }
      );

      return successResponse(
        response.data,
        "Employee fetched successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog(
        "Faild to fetch fetch attendance device list",
        "attendance-device",
        error
      );
      return errorResponse(
        error?.message || "Failed to fetch attendance device list",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async startEnrollment(id: number, data: StartEnrollmentDto) {
    // console.log(data);
    try {
      const businessMember = await BusinessMembersModel.findOne({
        where: { id },
      });

      if (!businessMember || !businessMember?.is_in_attendance_device) {
        return errorResponse(
          "Please update your official information",
          {},
          HttpStatus.NOT_FOUND
        );
      }
      if (businessMember.status !== "active") {
        return errorResponse(
          "Your account is not active",
          {},
          HttpStatus.FORBIDDEN
        );
      }

      const response = await axios.post(
        `${this.ATTENDANCE_DEVICE_URL}/devices/${data.deviceIdentifier}/startEnrollment`,
        {
          person_identifier: `${id}`,
          hand: data.hand,
          finger: data.finger,
        },
        {
          params: {
            api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
          },
        }
      );

      if (response.status !== 200 || response?.data?.code !== 200) {
        return errorResponse(
          response?.data?.message || "Failed to start enrollment",
          response.data || {},
          response.status || HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      const isEnrollmentAlreadyStarted =
        await AttendanceDeviceEnrollmentModel.findOne({
          where: {
            device_identifier: data.deviceIdentifier,
            business_member_id: Number(id),
            finger: data.finger,
            hand: data.hand,
          },
        });

      if (isEnrollmentAlreadyStarted) {
        await AttendanceDeviceEnrollmentModel.update(
          {
            updatedAt: new Date(),
          },
          {
            where: {
              device_identifier: data.deviceIdentifier,
              business_member_id: Number(id),
              finger: data.finger,
              hand: data.hand,
            },
          }
        );
      } else {
        const deviceEnrollment = await AttendanceDeviceEnrollmentModel.create({
          hand: data.hand,
          finger: data.finger,
          device_identifier: data.deviceIdentifier,
          business_member_id: businessMember.id,
          createdAt: new Date(),
        });
      }

      return successResponse(
        response.data,
        "Enrollment started successfully",
        HttpStatus.OK
      );
    } catch (error) {
      // console.log(error);
      ErrorLog("Faild to start enrollment", "attendance-device", error);
      return errorResponse(
        error?.response?.data?.message || "Failed to start enrollment",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async stopEnrollment(deviceIdentifier: string) {
    try {
      const response = await axios.post(
        `${this.ATTENDANCE_DEVICE_URL}/devices/${deviceIdentifier}/stopEnrollment`,
        {},
        {
          params: {
            api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
          },
        }
      );

      if (response.status !== 200 || response?.data?.code !== 200) {
        return errorResponse(
          response?.data?.message || "Failed to stop enrollment",
          response.data || {},
          response.status || HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      return successResponse(
        response.data,
        "Enrollment stopped successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Faild to stop enrollment", "attendance-device", error);
      return errorResponse(
        error?.message || "Failed to stop enrollment",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getEnrollmentData(userId: number) {
    try {
      const enrollmentData = await AttendanceDeviceEnrollmentModel.findAll({
        where: {
          business_member_id: userId,
        },
      });

      return successResponse(
        enrollmentData,
        "Enrollment data fetched successfully",
        HttpStatus.OK
      );
    } catch (error) {
      return errorResponse(
        error?.message || "Failed to get enrollment data",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async enrollmentStatus(data: EnrollmentStatusDto) {
    try {
      const response = await axios.get(
        `${this.ATTENDANCE_DEVICE_URL}/devices/enrollment_status`,
        {
          params: {
            api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
            device_id: data.deviceId,
            person_id: data.userId,
          },
        }
      );
      return successResponse(response.data, "Enrollment", HttpStatus.OK);
    } catch (error) {
      ErrorLog("Fetch enrollment status failed!", "attendance-device", error);
      return errorResponse(
        error?.message || "Fetch enrollment status failed!",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async allocateUserToADevice(
    deviceIdentifier: string,
    userIds: AllocateUserToDeviceDto[]
  ) {
    try {
      const userArray = userIds.map((user) => ({
        person_identifier: user.userId,
        action: "allocate",
      }));
      const response = await axios.post(
        `${this.ATTENDANCE_DEVICE_URL}/devices/${deviceIdentifier}/allocations`,
        userArray,
        {
          params: {
            api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
          },
        }
      );

      if (response.status === 200) {
        const usersToInsert = [];

        for (const data of response.data) {
          if (data.message != true) {
            continue;
          }
          const isUserAlreadyAllocated =
            await UserAttendanceDeviceModel.findOne({
              where: {
                device_identifier: deviceIdentifier,
                business_member_id: data.person_identifier,
              },
            });

          if (!isUserAlreadyAllocated) {
            usersToInsert.push({
              device_identifier: deviceIdentifier,
              business_member_id: data.person_identifier,
            });
          }
        }

        if (usersToInsert.length > 0) {
          await UserAttendanceDeviceModel.bulkCreate(usersToInsert);
        }
      }

      return successResponse(
        response.data,
        "Allocated user to a device successfully",
        HttpStatus.OK
      );
    } catch (error) {
      if (error?.response?.status === 404) {
        const usersToInsert = [];

        if (error?.response?.data && Array.isArray(error.response.data)) {
          for (const data of error.response.data) {
            if (data.message === true) {
              const isUserAlreadyAllocated =
                await UserAttendanceDeviceModel.findOne({
                  where: {
                    device_identifier: deviceIdentifier,
                    business_member_id: data.person_identifier,
                  },
                });

              if (!isUserAlreadyAllocated) {
                usersToInsert.push({
                  device_identifier: deviceIdentifier,
                  business_member_id: data.person_identifier,
                });
              }
            }
          }
          if (usersToInsert.length > 0) {
            await UserAttendanceDeviceModel.bulkCreate(usersToInsert);
          }
        }
      }

      ErrorLog("Allocate user to a device failed!", "attendance-device", error);
      return errorResponse(
        error?.message || "Allocate user to a device failed",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async revokeUserFromADevice(
    deviceIdentifier: string,
    userIds: AllocateUserToDeviceDto[]
  ) {
    try {
      const userArray = userIds.map((user) => ({
        person_identifier: +user.userId,
        action: "revoke",
      }));
      const response = await axios.post(
        `${this.ATTENDANCE_DEVICE_URL}/devices/${deviceIdentifier}/allocations`,
        userArray,
        {
          params: {
            api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
          },
        }
      );

      if (response.status === 200) {
        const usersToInsert = [];

        for (const data of response.data) {
          const isUserAlreadyAllocated =
            await UserAttendanceDeviceModel.findOne({
              where: {
                device_identifier: deviceIdentifier,
                business_member_id: +data.person_identifier,
              },
            });

          if (isUserAlreadyAllocated) {
            usersToInsert.push(+data.person_identifier);
          }
        }

        if (usersToInsert.length > 0) {
          await UserAttendanceDeviceModel.destroy({
            where: {
              device_identifier: deviceIdentifier,
              business_member_id: usersToInsert,
            },
          });
        }
      }

      return successResponse(
        response.data,
        "Revoke user from a device successfully",
        HttpStatus.OK
      );
    } catch (error) {
      if (error?.response?.status === 404) {
        const usersToInsert = [];

        if (error?.response?.data && Array.isArray(error.response.data)) {
          for (const data of error.response.data) {
            if (data.message === true) {
              const isUserAlreadyAllocated =
                await UserAttendanceDeviceModel.findOne({
                  where: {
                    device_identifier: deviceIdentifier,
                    business_member_id: data.person_identifier,
                  },
                });

              if (isUserAlreadyAllocated) {
                usersToInsert.push(Number(data.person_identifier));
              }
            }
          }

          if (usersToInsert.length > 0) {
            await UserAttendanceDeviceModel.destroy({
              where: {
                device_identifier: deviceIdentifier,
                business_member_id: usersToInsert,
              },
            });
          }
        }
      }
      return errorResponse(
        error?.message || "Revoke user from a device failed",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getAllocatedUsers(deviceIdentifier: string) {
    try {
      const employees = await BusinessMembersModel.findAndCountAll({
        where: { is_in_attendance_device: true },
        attributes: ["id", "employee_id", "mobile", "status"],
        include: [
          {
            model: UserAttendanceDeviceModel,
            where: { device_identifier: deviceIdentifier },
          },
          {
            model: MembersModel,
            attributes: ["id"],
            include: [
              {
                model: ProfilesModel,
                attributes: ["id", "name", "email", "mobile", "pro_pic"],
              },
            ],
          },
          {
            model: BusinessRolesModel,

            attributes: ["id", "name"], // Select specific attributes from BusinessRolesModel

            include: [
              {
                model: BusinessDepartmentsModel,
                attributes: ["id", "name"],
              },
            ],
          },
        ],
      });
      return successResponse(
        {
          totalLength: employees.count,
          employees: employees?.rows,
        },
        "Employee fetched successfully",
        HttpStatus.OK
      );
    } catch (error) {
      return errorResponse(
        error?.message || "Failed to get allocated users",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getUnAllocatedUsers(deviceIdentifier: string) {
    try {
      const assignedUsers = await UserAttendanceDeviceModel.findAll({
        where: {
          device_identifier: deviceIdentifier,
        },
        attributes: ["business_member_id"],
      });
      const assignedUserIds = assignedUsers.map(
        (user) => user.business_member_id
      );

      const employees = await BusinessMembersModel.findAndCountAll({
        where: {
          is_in_attendance_device: true,
          id: {
            [Op.notIn]: assignedUserIds, // Filter out the users who are assigned to the device
          },
        }, // is_in_attendance_device means user data in addendance device server
        attributes: ["id", "employee_id", "status", "mobile"],

        include: [
          {
            model: UserAttendanceDeviceModel,
            required: false,
            where: { device_identifier: { [Op.eq]: deviceIdentifier } },
          },
          {
            model: MembersModel,
            attributes: ["id"],
            include: [
              {
                model: ProfilesModel,
                attributes: ["id", "name", "email", "mobile", "pro_pic"],
              },
            ],
          },
          {
            model: BusinessRolesModel,

            attributes: ["id", "name"], // Select specific attributes from BusinessRolesModel

            include: [
              {
                model: BusinessDepartmentsModel,
                attributes: ["id", "name"],
              },
            ],
          },
        ],
      });

      return successResponse(
        {
          totalLength: employees.count,
          employees: employees?.rows,
        },
        "Employee fetched successfully",
        HttpStatus.OK
      );
    } catch (error) {
      return errorResponse(
        error?.message || "Failed to get allocated users",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // storing existing employee data to attendance device server
  async createMultipleEmployeeToDeviceSystem(
    createEmployeeToDeviceDto: CreateEmployeeToDeviceDto[]
  ) {
    try {
      const successResults = [];
      const failedResults = [];

      await Promise.all(
        createEmployeeToDeviceDto.map(async (employee) => {
          try {
            const formData = new FormData();
            formData.append("identifier", employee.identifier);
            formData.append("name", employee.name);
            formData.append("primary_display_text", "Welcome");
            formData.append("secondary_display_text", employee.name);
            formData.append("rfid", employee.identifier);

            if (employee.image_base64) {
              formData.append("image_base64", employee.image_base64);
            }

            const response = await axios.post(
              `${this.ATTENDANCE_DEVICE_URL}/people`,
              formData,
              {
                headers: {
                  ...formData.getHeaders(),
                },
                params: {
                  api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
                },
              }
            );

            if (response.status === 200) {
              await BusinessMembersModel.update(
                {
                  is_in_attendance_device: true,
                },
                {
                  where: {
                    id: +employee.identifier,
                  },
                }
              );

              successResults.push({
                employee: employee.identifier,
                message: "Employee created successfully",
                data: response.data,
              });
            } else {
              failedResults.push({
                employee: employee.identifier,
                error: "Failed to create employee",
                response: response.data,
              });
            }
          } catch (error) {
            failedResults.push({
              employee: employee.identifier,
              error: error?.message || "Failed to create employee",
              response: error?.response?.data || {},
            });

            // Log error for debugging
            ErrorLog(
              "employee create for attendance device",
              "attendance-device",
              error
            );
          }
        })
      );

      return successResponse({
        success: successResults,
        failed: failedResults,
      });
    } catch (error) {
      return errorResponse(
        error?.message || "Unexpected error while creating employees",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  //get attendance data from attendance device server by pulling data
  @Cron("*/2 * * * *") // Runs every 5 minutes
  // @Cron("*/10 * * * *") // Runs every 10 minutes
  // @Cron("*/20 * * * *") // Runs every 20 minutes
  // @Cron("10 * * * * *") //runs every 45 seconds
  // @Cron("0 * * * *") //runs every hour
  async fetchAttendanceData() {
    console.log("fetching attendance data");
    try {
      const start = new Date().toISOString().split("T")[0];
      const end = new Date().toISOString().split("T")[0];
      let page = 1;
      let hasMoreData = true;

      while (hasMoreData) {
        const response = await axios.get(`${this.ATTENDANCE_DEVICE_URL}/logs`, {
          params: {
            api_token: this.ATTENDANCE_DEVICE_API_TOKEN,
            start,
            end,
            page,
            sync_time: "sync_time",
            order_direction: "asc",
            order_key: "sync_time",
          },
        });

        if (response.status === 200 && response.data?.data.length > 0) {
          const attendanceLogs: AttendanceLog[] = response.data.data;

          const { checkInTimes, checkOutTimes } =
            await this.processAttendanceData(attendanceLogs);

          if (checkInTimes.length > 0) {
            console.log("checkInTimes", checkInTimes);
            await this.sendCheckInData(checkInTimes);
          }

          if (checkOutTimes.length > 0) {
            console.log("checkOutTimes", checkOutTimes);
            await this.sendCheckOutData(checkOutTimes);
          }
        }

        if (!response.data.links.next) {
          hasMoreData = false;
        } else {
          page++;
        }
      }
    } catch (error) {
      console.error("Error fetching attendance data", error);
      ErrorLog("Failed to fetch attendance data", "attendance-device", error);
    }
  }

  private async processAttendanceData(data: AttendanceLog[]) {
    const groupedData = new Map<string, AttendanceLog[]>();

    // Group data by person_identifier
    for (const entry of data) {
      const personKey = entry.person_identifier;

      if (!groupedData.has(personKey)) {
        groupedData.set(personKey, [entry]);
      } else {
        groupedData.get(personKey)!.push(entry);
      }
    }

    const checkInTimes: CheckInCheckOutBodyLog[] = [];
    const checkOutTimes: CheckInCheckOutBodyLog[] = [];

    // Process each group
    // groupedData.forEach(async (entries) => {
    for (const [personId, entries] of groupedData.entries()) {
      if (entries.length === 1) {
        const businessMember = await BusinessMembersModel.findOne({
          where: { id: +entries[0].person_identifier },
        });
        if (businessMember) {
          checkInTimes.push({
            business_member_id: businessMember.id,
            business_id: businessMember.business_id,
            date: entries[0].logged_time.split(" ")[0],
            time: entries[0].logged_time.split(" ")[1],
            device_id: entries[0].device_identifier,
            location: JSON.stringify({ address: entries[0].location }),
          });
        }
      } else {
        // Find check-in (earliest logged_time) and check-out (latest logged_time)
        // const checkIn = entries.reduce((min, entry) =>
        //   new Date(entry.logged_time) < new Date(min.logged_time) ? entry : min
        // );
        // const checkOut = entries.reduce((max, entry) =>
        //   new Date(entry.logged_time) > new Date(max.logged_time) ? entry : max
        // );

        entries.sort(
          (a, b) =>
            new Date(a.logged_time).getTime() -
            new Date(b.logged_time).getTime()
        );

        const checkIn = entries[0];
        const checkOut = entries[entries.length - 1];

        const checkInBusinessMember = await BusinessMembersModel.findOne({
          where: { id: +checkIn.person_identifier },
        });

        if (checkInBusinessMember) {
          checkInTimes.push({
            business_member_id: checkInBusinessMember.id,
            business_id: checkInBusinessMember.business_id,
            date: checkIn.logged_time.split(" ")[0],
            time: checkIn.logged_time.split(" ")[1],
            device_id: checkIn.device_identifier,
            location: JSON.stringify({ address: checkIn.location }),
          });
        }

        const checkOutBusinessMember = await BusinessMembersModel.findOne({
          where: { id: +checkIn.person_identifier },
        });

        if (checkOutBusinessMember) {
          checkOutTimes.push({
            business_member_id: checkOutBusinessMember.id,
            business_id: checkOutBusinessMember.business_id,
            date: checkOut.logged_time.split(" ")[0],
            time: checkOut.logged_time.split(" ")[1],
            device_id: checkOut.device_identifier,
            location: JSON.stringify({ address: checkOut.location }),
          });
        }
      }
    }

    return { checkInTimes, checkOutTimes };
  }

  private async sendCheckInData(data: CheckInCheckOutBodyLog[]) {
    try {
      const response = await axios.post(
        `${this.ATTENDANCE_SERVICE_API_URL}/attendance-api/attendance/v1/check-in-device`,
        {
          check_in: data,
        }
      );

      if (response.status === 200) {
        console.log("Check-in data successfully sent");
      } else {
        ErrorLog(
          "Faild to stop enrollment",
          "attendance-device",
          response.data
        );
      }
    } catch (error) {
      console.error("Error sending check-in data", error?.response?.data);
      ErrorLog(
        "Failed to send attendance check-in data",
        "attendance-device",
        error?.response?.data
      );
    }
  }

  private async sendCheckOutData(data: CheckInCheckOutBodyLog[]) {
    console.log("Sending check-out data");
    try {
      await BusinessMembersModel;
      const response = await axios.post(
        `${this.ATTENDANCE_SERVICE_API_URL}/attendance-api/attendance/v1/check-out-device`,
        {
          check_out: data,
        }
      );

      if (response.status === 200) {
        console.log("Check-out data successfully sent");
      } else {
        ErrorLog(
          "Failed to send attendance check-out data",
          "attendance-device",
          response?.data
        );
      }
    } catch (error) {
      console.error(
        "Failed to send attendance check-out data",
        error?.response?.data
      );
      ErrorLog(
        "Failed to send attendance check-out data",
        "attendance-device",
        error?.response?.data
      );
    }
  }
}
