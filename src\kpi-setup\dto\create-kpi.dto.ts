import { Type } from "class-transformer";
import {
  <PERSON><PERSON><PERSON>y,
  IsDate,
  IsNotEmpty,
  IsString,
  ValidateNested,
} from "class-validator";

class DepartmentDto {
  @IsNotEmpty()
  departmentId: number;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  due_date: Date;
}

export class CreateKpiDto {
  @IsNotEmpty()
  @IsString()
  kpi_title: string;

  @IsNotEmpty()
  @IsString()
  plan_type: string;

  @IsNotEmpty()
  @IsString()
  plan_period: string;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  cycle_time_start: Date;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  cycle_time_end: Date;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DepartmentDto)
  departments: DepartmentDto[];
}
