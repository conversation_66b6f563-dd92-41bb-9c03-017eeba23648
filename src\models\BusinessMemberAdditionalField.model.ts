import { Model, Table, Column, DataType, ForeignKey, HasMany, BelongsTo } from 'sequelize-typescript';
import { BusinessMemberAdditionalSectionsModel } from './BusinessMemberAdditionalSection.model';
import { BusinessMemberAdditionalDataModel } from './BusinessMemberAdditionalData.model';
@Table({
  tableName: 'business_member_additional_fields',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class BusinessMemberAdditionalFieldsModel extends Model<BusinessMemberAdditionalFieldsModel> {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => BusinessMemberAdditionalSectionsModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  section_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  label: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  type: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  rules: any;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  created_by: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  created_by_name: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  updated_by: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  updated_by_name: string;

  //relation
    @BelongsTo(() => BusinessMemberAdditionalSectionsModel)
    section: BusinessMemberAdditionalSectionsModel; // section_id

    @HasMany(() => BusinessMemberAdditionalDataModel)
    additional_data: BusinessMemberAdditionalDataModel[];
}