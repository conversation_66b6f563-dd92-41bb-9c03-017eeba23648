import {
	Table,
	Column,
	Model,
	DataType,
	ForeignKey,
	BelongsTo,
	CreatedAt,
	UpdatedAt,
} from "sequelize-typescript";
import { KpiModel } from "./kpi.model";
import { BusinessDepartmentsModel } from "./BusinessDepartment.model";

@Table({
	tableName: "kpi_departments",
	timestamps: true,
})
export class KpiDepartmentModel extends Model<KpiDepartmentModel> {
	@Column({
		type: DataType.BIGINT,
		primaryKey: true,
		autoIncrement: true,
	})
	id: number;

	@ForeignKey(() => KpiModel)
	@Column({
		type: DataType.INTEGER,
		allowNull: false,
	})
	kpiId: number;

	@ForeignKey(() => BusinessDepartmentsModel)
	@Column({
		type: DataType.INTEGER,
		allowNull: false,
	})
	departmentId: number;

	@Column({
		type: DataType.DATE,
		allowNull: true,
	})
	due_date: Date;

	@BelongsTo(() => KpiModel, { foreignKey: "kpiId" })
	kpi: KpiModel;

	@BelongsTo(() => BusinessDepartmentsModel)
	department: BusinessDepartmentsModel;

	@CreatedAt
	@Column({ field: "created_at" })
	created_at: Date;

	@UpdatedAt
	@Column({ field: "updated_at" })
	updated_at: Date;
}
