import {
	AutoIncrement,
	Column,
	CreatedAt,
	DataType,
	Has<PERSON>any,
	Model,
	PrimaryKey,
	Table,
	UpdatedAt,
} from "sequelize-typescript";
import { KpiDepartmentModel } from "./kpiDepartment.model";
import { AppraisalDepartmentModel } from "./appraisal-department-model";
import { EmployeeKpiModel } from "./employeeKpi.model";

@Table({
	tableName: "kpi",
	timestamps: true,
	createdAt: "created_at",
	updatedAt: "updated_at",
})
export class KpiModel extends Model<KpiModel> {
	@PrimaryKey
	@AutoIncrement
	@Column
	id: number;

	@Column({
		type: DataType.STRING,
		allowNull: false,
	})
	kpi_title: string;

	@Column({
		type: DataType.STRING,
		allowNull: false,
	})
	plan_type: string;

	@Column({
		type: DataType.STRING,
		allowNull: false,
	})
	plan_period: string;

	@Column({
		type: DataType.DATE,
		allowNull: false,
	})
	cycle_time_start: Date;

	@Column({
		type: DataType.DATE,
		allowNull: false,
	})
	cycle_time_end: Date;

	@HasMany(() => KpiDepartmentModel, {
		foreignKey: "kpiId",
		as: "departments",
	})
	departments: KpiDepartmentModel[];

	@CreatedAt
	@Column({ field: "created_at" })
	created_at: Date;

	@UpdatedAt
	@Column({ field: "updated_at" })
	updated_at: Date;

	@HasMany(() => AppraisalDepartmentModel, {
		foreignKey: "kpiId",
		as: "appraisalDepartments",
	})
	appraisalDepartments: AppraisalDepartmentModel[];

	@HasMany(() => EmployeeKpiModel, {
		foreignKey: "kpiId", // Ensure this matches the foreign key in EmployeeKpiModel
		as: "employeeKpis",
	})
	employeeKpis: EmployeeKpiModel[];
}
