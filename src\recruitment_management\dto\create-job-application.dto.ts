// src/recruitment_management/dto/create-job-application.dto.ts

import { IsNotEmpty, IsString, IsEmail, IsOptional, IsInt } from 'class-validator';

export class CreateJobApplicationDto {
  @IsNotEmpty()
  @IsInt()
  job_post_id: number;

  @IsNotEmpty()
  @IsString()
  full_name: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  mobile: string;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  cv_link?: string;

  @IsOptional()
  @IsString()
  inquiry?: string;
}