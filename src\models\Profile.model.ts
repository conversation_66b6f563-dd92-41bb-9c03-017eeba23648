import { Table, Column, Model, DataType, <PERSON><PERSON><PERSON>, Has<PERSON>any } from 'sequelize-typescript';
import { MembersModel } from './Member.model';
import { ProfileBankInformationsModel } from './ProfileBankInformation.model';
import {
  GENDER,
  PORTAL_NAME,
  BLOOD_GROUP,
} from '../constants/api.enums';

//@Table({ tableName: 'profiles', timestamps: true })
@Table({ tableName: 'profiles'})
export class ProfilesModel extends Model {
  @Column({
    type: DataType.BIGINT,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  driver_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  bn_name: string;
  
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mobile: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  email: string;

  @Column({ // encrypted value
    type: DataType.STRING,
    allowNull: true,
  })
  password: string;

  
	@Column({
		// encrypted value
		type: DataType.STRING,
		allowNull: true,
	})
	new_password: string;

  @Column({ // encrypted value
    type: DataType.STRING,
    allowNull: true,
  })
  remember_token: string;

  @Column({ 
    type: DataType.INTEGER,
    defaultValue: 0, // value is 0,1
    allowNull: false
  })
  is_blacklisted: string;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW, // discuss with Jubair
    allowNull: true
  })
  login_blocked_until: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  fb_id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  google_id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  apple_id: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0, // value is 0,1
    allowNull: false
  })
  mobile_verified: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0, // value is 0,1
    allowNull: false
  })
  email_verified: number;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW, // discuss with Jubair
    allowNull: true
  })
  email_verified_at: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  address: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  father_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  mother_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  post_office: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  post_code: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  nationality: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  permanent_address: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  bkash_agreement_id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  occupation: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  nid_no: string;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW, // discuss with Jubair
    allowNull: true
  })
  nid_issue_date: Date;

  @Column({
    type: DataType.INTEGER,
    //defaultValue: 0, // maybe value is 0,1
    allowNull: true
  })
  nid_verified: number;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW, // discuss with Jubair
    allowNull: true
  })
  nid_verification_date: Date;
  
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  nid_address: string;
  
  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW, // discuss with Jubair
    allowNull: true
  })
  last_nid_verification_request_date: Date;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0, // incremented value
    allowNull: true
  })
  nid_verification_request_count: number;
  
  @Column({ // https://s3.ap-south-1.amazonaws.com/cdn-shebaxyz/images/resources/nid/1486989774_md._ibrahim_biswas.JPG
    type: DataType.STRING,
    allowNull: true
  })
  nid_image_front: string;
  
  @Column({ // https://s3.ap-south-1.amazonaws.com/cdn-shebaxyz/images/resources/nid/1486989774_md._ibrahim_biswas.JPG
    type: DataType.STRING,
    allowNull: true
  })
  nid_image_back: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  passport_no: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  passport_image: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tin_no: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tin_certificate: string;
  
  @Column({
    type: DataType.STRING, // Dynamically load enum values
    allowNull: true,
    //defaultValue: GENDER.OTHER,
  })
  gender: GENDER;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW, // discuss with Jubair
    allowNull: true
  })
  dob: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  birth_place: string;

  @Column({ // https://s3.ap-south-1.amazonaws.com/cdn-shebadev/images/profiles/pro_pic_1552910734_pro_pic_image_1.png
    type: DataType.STRING,
    allowNull: true
  })
  pro_pic: string;

  @Column({
    type: DataType.DECIMAL(11,10),
    defaultValue: 1000.00,
    allowNull: false
  })
  total_asset_amount: number;

  @Column({
    type: DataType.DECIMAL(11,10),
    defaultValue: 0.00,
    allowNull: false
  })
  monthly_living_cost: number;

  @Column({
    type: DataType.DECIMAL(11,10),
    defaultValue: 0.00,
    allowNull: false
  })
  monthly_loan_installment_amount: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  utility_bill_attachment: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  nominee_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  nominee_relation: string;

  @Column({ // Primary key of profiles table
    type: DataType.INTEGER,
    allowNull: true,
  })
  grantor_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  grantor_relation: string;

  @Column({
    type: DataType.STRING,
    //defaultValue: PORTAL_NAME.ADMIN_PORTAL,
    allowNull: true
  })
  portal_name: PORTAL_NAME;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  ip: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  user_agent: string;
  
  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false
  })
  created_by: number; // Profiles table primary key
  
  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  created_by_name: string; // Profiles table name

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false
    //allowNull: true
  })
  updated_by: number; // Profiles table primary key
  
  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  updated_by_name: string; // Profiles table name

  @Column({
    type: DataType.STRING,
    //defaultValue: BLOOD_GROUP.OTHER,
    allowNull: true
  })
  blood_group: BLOOD_GROUP;
  
  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: false
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: true // maybe allowNull: true
  })
  updated_at: Date;

  // @Column({
  //   type: DataType.DATE,
  //   allowNull: true,
  // })
  // deletedAt: Date;

    // Relationships
    @HasOne(() => MembersModel, { foreignKey: 'profile_id', as: 'member' })
    member: MembersModel;
  
    @HasOne(() => ProfileBankInformationsModel, { foreignKey: 'profile_id', as: 'bank_info' })
    bank_info: ProfileBankInformationsModel;
}