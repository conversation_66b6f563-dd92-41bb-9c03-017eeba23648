import { IsNotEmpty, IsOptional, IsString, IsInt, IsObject } from 'class-validator';
import { PartialType } from '@nestjs/swagger';
import { CreateEmployeeAdditionalFieldDto } from './create-employee-additional-field.dto';

//export class UpdateEmployeeAdditionalFieldDto extends PartialType(CreateEmployeeAdditionalFieldDto) {}
export class UpdateEmployeeAdditionalFieldDto {
    @IsNotEmpty()
    @IsInt()
    section_id: number;

    @IsNotEmpty()
    @IsString()
    name?: string;

    @IsNotEmpty()
    @IsString()
    label?: string;

    @IsNotEmpty()
    @IsString()
    type?: string;

    @IsOptional()
    @IsObject()
    rules: {
        extensions: string;
    }
}
