import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { JobPostModel } from './JobPost.model';

@Table({ tableName: 'job_applications', timestamps: true })
export class JobApplicationModel extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => JobPostModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  job_post_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  full_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  mobile: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  address: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  cv_link: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  inquiry: string;

  @BelongsTo(() => JobPostModel)
  job_post: JobPostModel;
}