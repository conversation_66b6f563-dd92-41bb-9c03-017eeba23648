import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AttendanceDeviceModule } from "src/attendance-device/attendance-device.module";
import { AuthService } from "src/auth/auth.service";
import { DigitalOceanService } from "src/file-upload/digital-ocean.service";
import { FileUploadService } from "src/file-upload/file-upload.service";
import { InviteEmployeeMail } from "../../utils/mail/invite-employee";
import { DatabaseModule } from "../config/database/database.module";
import { EmployeeController } from "./employee.controller";
import { EmployeeService } from "./employee.service";

@Module({
  imports: [DatabaseModule, AttendanceDeviceModule],
  controllers: [EmployeeController],
  providers: [
    EmployeeService,
    InviteEmployeeMail,
    AuthService,
    FileUploadService,
    DigitalOceanService,
  ],
})
export class EmployeeModule {}
