import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { StickerCategoryModel } from './sticker-category.model';

@Table({
    tableName: 'stickers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
})
export class StickerModel extends Model<StickerModel> {
    @Column({
        type: DataType.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    })
    id: number;

    @ForeignKey(() => StickerCategoryModel)
    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    sticker_category_id: number;

    @Column({
        type: DataType.STRING,
        allowNull: false,
    })
    image: string;

    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    created_by: number;

    @Column({
        type: DataType.STRING,
        allowNull: true,
    })
    created_by_name: string;

    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    updated_by: number;

    @Column({
        type: DataType.STRING,
        allowNull: true,
    })
    updated_by_name: string;

    @Column({
        type: DataType.DATE,
        allowNull: false,
    })
    created_at: Date;

    @Column({
        type: DataType.DATE,
        allowNull: false,
    })
    updated_at: Date;

    @BelongsTo(() => StickerCategoryModel)
    stickerCategory: StickerCategoryModel;
}