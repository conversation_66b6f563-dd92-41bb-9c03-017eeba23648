import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    UseGuards,
    Req,
    Res,
} from "@nestjs/common";
import { TeammateAppreciationService } from "./teammate-appreciation.service";
import { CreateTeammateAppreciationDto } from "./dto/create-teammate-appreciation.dto";
import { ServiceTokenGuard } from "../guards";
import { Response } from "express";
import { UpdateTeammateAppreciationDto } from "./dto/update-teammate-appreciation.dto";

@Controller("teammate-appreciation")
export class TeammateAppreciationController {
    constructor(
        private readonly teammateAppreciationService: TeammateAppreciationService
    ) {}

    @UseGuards(ServiceTokenGuard)
    @Get("v1/get-sticker")
    async create(
        @Req() req,
        @Res() res: Response,
        @Query("sticker_category_id") stickerCategoryId?: string
    ) {
        const data = await this.teammateAppreciationService.getSticker(
            req?.user,
            stickerCategoryId
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Post("v1/appreciation-submit")
    async createAppreciation(
        @Body() createTeammateAppreciationDto: CreateTeammateAppreciationDto,
        @Req() req,
        @Res() res: Response
    ) {
        const data = await this.teammateAppreciationService.createAppreciation(
            createTeammateAppreciationDto,
            req?.user,
            req?.headers,
        );
        return res.status(data.statusCode).json(data);
    }
    
    @UseGuards(ServiceTokenGuard)
    @Patch("v1/appreciation-note-update/:id")
    async updateAppreciationNote(
        @Param("id") id: number,
        @Body() updateTeammateAppreciationDto: UpdateTeammateAppreciationDto,
        @Req() req,
        @Res() res: Response
    ) {
        const data = await this.teammateAppreciationService.updateAppreciationNote(
            id,
            updateTeammateAppreciationDto,
            req?.user
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Get("v1/teammate-list-with-appreciation")
    async getEmployeeAppreciationList(
        @Query("business_department_id") business_department_id: number,
        @Query("status") status: string,
        @Req() req,
        @Res() res: Response
    ) {
        const data =
            await this.teammateAppreciationService.getEmployeeAppreciationList(
                business_department_id,
                status,
                req?.user
            );
        return res.status(data.statusCode).json(data);
    }
}
