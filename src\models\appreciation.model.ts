import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { StickerModel } from './sticker.model';
import { BusinessMembersModel } from './BusinessMember.model';

@Table({
    tableName: 'appreciations',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
})
export class AppreciationModel extends Model<AppreciationModel> {
    @Column({
        type: DataType.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    })
    id: number;

    @ForeignKey(() => BusinessMembersModel)
    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    receiver_id: number;

    @ForeignKey(() => BusinessMembersModel)
    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    giver_id: number;

    @ForeignKey(() => StickerModel)
    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    sticker_id: number;

    @Column({
        type: DataType.STRING,
        allowNull: true,
    })
    note: string;

    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    created_by: number;

    @Column({
        type: DataType.STRING,
        allowNull: true,
    })
    created_by_name: string;

    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    updated_by: number;

    @Column({
        type: DataType.STRING,
        allowNull: true,
    })
    updated_by_name: string;

    @Column({
        type: DataType.DATE,
        allowNull: false,
    })
    created_at: Date;

    @Column({
        type: DataType.DATE,
        allowNull: false,
    })
    updated_at: Date;

    @BelongsTo(() => StickerModel, {
        foreignKey: 'sticker_id'
    })
    sticker: StickerModel;

    @BelongsTo(() => BusinessMembersModel, {
        foreignKey: 'receiver_id'
    })
    receiver: BusinessMembersModel;

    @BelongsTo(() => BusinessMembersModel, {
        foreignKey: 'giver_id'
    })
    giver: BusinessMembersModel;
}