"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.createTable("kpi_departments", {
			id: {
				type: Sequelize.BIGINT,
				autoIncrement: true,
				primaryKey: true,
			},
			kpiId: {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: "kpi", // table name for KpiModel
					key: "id",
				},
				onUpdate: "CASCADE",
				onDelete: "CASCADE",
			},
			departmentId: {
				type: Sequelize.INTEGER.UNSIGNED,
				allowNull: false,
				references: {
					model: "business_departments", // table name for BusinessDepartmentsModel
					key: "id",
				},
				onUpdate: "CASCADE",
				onDelete: "CASCADE",
			},
			due_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
			created_at: {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
			},
			updated_at: {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
			},
		});
	},

	down: async (queryInterface, Sequelize) => {
		await queryInterface.dropTable("kpi_departments");
	},
};
