import { IsOptional, IsString, IsInt } from 'class-validator';

export class UpdateBusinessRoleDto {
  @IsOptional()
  @IsInt()
  business_department_id?: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsInt()
  is_published?: number;

  @IsOptional()
  @IsInt()
  created_by?: number;

  @IsOptional()
  @IsString()
  created_by_name?: string;

  @IsOptional()
  @IsInt()
  updated_by?: number;

  @IsOptional()
  @IsString()
  updated_by_name?: string;
}