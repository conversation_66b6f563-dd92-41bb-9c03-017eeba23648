import { IsNotEmpty, IsOptional, IsString, IsEmail, IsDate, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
export class ActivateInviteEmployeeDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  gender: string;

  @IsOptional()
  @IsNumber()
  department_id?: number;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsNumber()
  business_role_id?: number;

  @IsOptional()
  @IsNumber()
  gross_salary?: number;

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  join_date?: Date;
}
