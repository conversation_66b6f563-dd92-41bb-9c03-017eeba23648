// For businesses table
export enum COMPANY_TYPE {
	COMPANY = "company",
	ORGANIZATION = "organization",
	INSTITUTION = "institution",
}
export enum HALF_DAY_CONFIGURATION {
	ENABLE = 1,
	DISABLE = 0,
}
export enum SHIFT_CONFIGURATION {
	ENABLE = 1,
	DISABLE = 0,
}
export enum LIVE_TRACK_CONFIGURATION {
	ENABLE = 1,
	DISABLE = 0,
}
export enum EMPLOYEE_VISIT_CONFIGURATION {
	ENABLE = 1,
	DISABLE = 0,
}
export enum PAYROLL_CONFIGURATION {
	ENABLE = 1,
	DISABLE = 0,
}
export enum FISCAL_YEAR_CONFIGURATION {
	January = 1,
	February = 2,
	March = 3,
	April = 4,
	May = 5,
	June = 6,
	July = 7,
	August = 8,
	September = 9,
	October = 10,
	November = 11,
	December = 12,
}
export enum SANDWICH_LEAVE_CONFIGURATION {
	ENABLE = 1,
	DISABLE = 0,
}
export enum LEAVE_PRORATE_CONFIGURATION {
	ENABLE = 1,
	DISABLE = 0,
}

// profiles table enum
export enum GENDER {
	MALE = "male",
	FEMALE = "female",
	OTHER = "other",
}
//enum('admin-portal','partner-portal','manager-app','customer-app','customer-portal','resource-portal','resource-app','bondhu-app','bondhu-portal','automatic','business-portal')
export enum PORTAL_NAME {
	ADMIN_PORTAL = "admin-portal",
	PARTNER_PROTAL = "partner-portal",
	MANAGER_APP = "manager-app",
	CUSTOMER_APP = "customer-app",
	CUSTOMER_PORTAL = "customer-portal",
	RESOURCE_PORTAL = "resource-portal",
	RESOURCE_APP = "resource-app",
	BONDHU_APP = "bondhu-app",
	BONDHU_PORTAL = "bondhu-portal",
	AUTOMATIC = "automatic",
	BUSINESS_PORTAL = "business-portal",
}
export enum BLOOD_GROUP {
	A_POSITIVE = "A+",
	A_NEGATIVE = "A-",
	B_POSITIVE = "B+",
	B_NEGATIVE = "B-",
	O_POSITIVE = "O+",
	O_NEGATIVE = "O-",
	AB_POSITIVE = "AB+",
	AB_NEGATIVE = "AB-",
}

// businesses table enum
export enum BUSINESS_MEMBER_TYPE {
	ADMIN = "admin",
	MANAGER = "manager",
	EDITOR = "editor",
	EMPLOYEE = "employee",
}
export enum EMPLOYEE_TYPE {
	PERMANENT = "permanent",
	ON_PROBATION = "on_probation",
	CONTRACTUAL = "contractual",
	INTERN = "intern",
}
export enum EMPLOYEE_STATUS {
	ACTIVE = "active",
	INACTIVE = "inactive",
	INVITED = "invited",
}

// profiles bank information table enum
export enum PURPOSE {
	PWW = "partner_wallet_withdrawal",
	OTHER = "other",
}

export enum BANK_LIST {
	city_bank = "city_bank",
	ab_bank = "ab_bank",
	bank_asia = "bank_asia",
	brac_bank = "brac_bank",
	dhaka_bank = "dhaka_bank",
	dutch_bangla_bank = "dutch_bangla_bank",
	eastern_bank = "eastern_bank",
	ific_bank = "ific_bank",
	jamuna_bank = "jamuna_bank",
	meghna_bank = "meghna_bank",
	shonali_bank = "shonali_bank",
	modhumoti_bank = "modhumoti_bank",
	mutual_trust_bank = "mutual_trust_bank",
	national_bank = "national_bank",
	nrb_bank = "nrb_bank",
	nrb_commercial_bank = "nrb_commercial_bank",
	nrb_global_bank = "nrb_global_bank",
	one_bank = "one_bank",
	padma_bank = "padma_bank",
	premier_bank = "premier_bank",
	PRIME_BANK = "PRIME_BANK",
	pubali_bank = "pubali_bank",
	shimanto_bank = "shimanto_bank",
	south_bangla_agriculture_and_commerce_bank = "south_bangla_agriculture_and_commerce_bank",
	standard_bank = "standard_bank",
	trust_bank = "trust_bank",
	united_commercial_bank = "united_commercial_bank",
	uttara_bank = "uttara_bank",
	southeast_bank = "southeast_bank",
	community_bank_bangladesh = "community_bank_bangladesh",
	mercantile_bank = "mercantile_bank",
	national_credit_and_commerce_bank = "national_credit_and_commerce_bank",
	janata_bank = "janata_bank",
	agrani_bank = "agrani_bank",
	rupali_bank = "rupali_bank",
	basic_bank = "basic_bank",
	bangladesh_development_bank = "bangladesh_development_bank",
	bangladesh_krishi_bank = "bangladesh_krishi_bank",
	rajshahi_krishi_unnayan_bank = "rajshahi_krishi_unnayan_bank",
	probashi_kallyan_bank = "probashi_kallyan_bank",
	standard_chartered_bank = "standard_chartered_bank",
	bank_al_falah = "bank_al_falah",
	al_arafah_islami_bank = "al_arafah_islami_bank",
	first_security_islami_bank = "first_security_islami_bank",
	icb_islamic_bank = "icb_islamic_bank",
	islami_bank_bangladesh = "islami_bank_bangladesh",
	shahjalal_islami_bank = "shahjalal_islami_bank",
	social_islami_bank = "social_islami_bank",
	union_bank = "union_bank",
}

// export enum BANK_LIST {
//     "city_bank",
//     "ab_bank",
//     "bank_asia",
//     "brac_bank",
//     "dhaka_bank",
//     "dutch_bangla_bank",
//     "eastern_bank",
//     "ific_bank",
//     "jamuna_bank",
//     "meghna_bank",
//     "shonali_bank",
//     "modhumoti_bank",
//     "mutual_trust_bank",
//     "national_bank",
//     "nrb_bank",
//     "nrb_commercial_bank",
//     "nrb_global_bank",
//     "one_bank",
//     "padma_bank",
//     "premier_bank",
//     "PRIME_BANK",
//     "pubali_bank",
//     "shimanto_bank",
//     "south_bangla_agriculture_and_commerce_bank",
//     "standard_bank",
//     "trust_bank",
//     "united_commercial_bank",
//     "uttara_bank",
//     "southeast_bank",
//     "community_bank_bangladesh",
//     "mercantile_bank",
//     "national_credit_and_commerce_bank",
//     "janata_bank",
//     "agrani_bank",
//     "rupali_bank",
//     "basic_bank",
//     "bangladesh_development_bank",
//     "bangladesh_krishi_bank",
//     "rajshahi_krishi_unnayan_bank",
//     "probashi_kallyan_bank",
//     "standard_chartered_bank",
//     "bank_al_falah",
//     "al_arafah_islami_bank",
//     "first_security_islami_bank",
//     "icb_islamic_bank",
//     "islami_bank_bangladesh",
//     "shahjalal_islami_bank",
//     "social_islami_bank",
//     "union_bank"
// }
export enum ACCOUNT_TYPE {
	SAVINGS = "savings",
	CURRENT = "current",
}
