import { IsNotEmpty, Is<PERSON>ptional, IsString, IsEmail, IsDate, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
export class CreateEmployeeDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  gender: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsNumber()
  department_id?: number;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsNumber()
  business_role_id?: number;

  @IsOptional()
  @IsNumber()
  salary?: number;

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  join_date?: Date;
}
