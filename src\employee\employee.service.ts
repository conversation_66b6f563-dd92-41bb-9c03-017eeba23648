import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
} from "@nestjs/common";
import { Op, QueryTypes } from "sequelize";
import { Sequelize } from "sequelize-typescript";

import * as bcrypt from "bcrypt";
// import { Sequelize } from "sequelize-typescript";
import { addMonths, format, subDays } from "date-fns";
import { BusinessesModel } from "../models/Business.model";
import { BusinessDepartmentsModel } from "../models/BusinessDepartment.model";
import { BusinessMembersModel } from "../models/BusinessMember.model";
import { BusinessMemberAdditionalDataModel } from "../models/BusinessMemberAdditionalData.model";
import { BusinessMemberAdditionalFieldsModel } from "../models/BusinessMemberAdditionalField.model";
import { BusinessMemberAdditionalSectionsModel } from "../models/BusinessMemberAdditionalSection.model";
import { BusinessMemberBkashInfoModel } from "../models/BusinessMemberBkashInfo.model";
import { BusinessMemberStatusChangeLogModel } from "../models/BusinessMemberStatusChangeLog.model";
import { BusinessRolesModel } from "../models/BusinessRole.model";
import { MembersModel } from "../models/Member.model";
import { ProfilesModel } from "../models/Profile.model";
import { ProfileBankInformationsModel } from "../models/ProfileBankInformation.model";
import {
  AUTH_API_URL,
  DATABASE_CONNECTION,
  DEFAULT_PROFILE_PIC,
  HOLIDAY_API_URL,
  PAYROLL_API_URL,
  SHIFT_API_URL,
} from "./../config/constants";
import { CreateEmployeeDto } from "./dto/create-employee.dto";
import { ReinviteEmailDto } from "./dto/reinvite-email.dto";
import { updateEmployeeAdditionalDataDto } from "./dto/update-employee-additional-data.dto";
import { updateEmployeeEmergencyContactDataDto } from "./dto/update-employee-emergency-contact-data.dto";
import { updateEmployeeFinancialDataDto } from "./dto/update-employee-financial-data.dto";
import { updateEmployeeOfficialDataDto } from "./dto/update-employee-official-data.dto";
import { updateEmployeePersonalDataDto } from "./dto/update-employee-personal-data.dto";

import axios from "axios";
import * as fs from "fs";
import * as hbs from "handlebars";
import * as pdf from "html-pdf";
import moment from "moment-timezone";
import { toWords } from "number-to-words";
import * as path from "path";
import { AttendanceDeviceService } from "src/attendance-device/attendance-device.service";
import { ErrorLog } from "src/config/winstonLog";
import { SalariesModel } from "src/models";
import { AppreciationModel } from "src/models/appreciation.model";
import { StickerModel } from "src/models/sticker.model";
import { errorResponse, successResponse } from "utils/response";
import { InviteEmployeeMail } from "../../utils/mail/invite-employee";
import {
  BUSINESS_MEMBER_TYPE,
  EMPLOYEE_STATUS,
  EMPLOYEE_TYPE,
  GENDER,
} from "../constants/api.enums";
import { FileUploadService } from "../file-upload/file-upload.service";
import { ActivateInviteEmployeeDto } from "./dto/activate-invite-employee.dto";
import { EmployeeStatusBulkUpdateDto } from "./dto/employee-status-bulk-update.dto";
import { InviteEmployeeDto } from "./dto/invite-employee.dto";
import { UpdateMemberTypesIDDto } from "./dto/update-member-types-id.dto";
import { UpdateMemberTypesDto } from "./dto/update-member-types.dto";

@Injectable()
export class EmployeeService {
  private readonly defaultProfilePic = DEFAULT_PROFILE_PIC;

  constructor(
    private readonly InviteEmployeeMailService: InviteEmployeeMail,
    @Inject(DATABASE_CONNECTION) private readonly sequelize: Sequelize, // Injecting the Sequelize instance
    private readonly fileUploadService: FileUploadService,
    private readonly attendanceDeviceService: AttendanceDeviceService
  ) {}

  async inviteWithEmail(
    inviteEmployeeDto: InviteEmployeeDto,
    loginUser: any
  ): Promise<any> {
    const { email } = inviteEmployeeDto;
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      const transaction = await this.sequelize.transaction();
      try {
        for (const emailAddress of email) {
          let profile = await ProfilesModel.findOne({
            where: { email: emailAddress },
            transaction,
          });
          if (profile) {
            throw new HttpException(
              "Email already exists",
              HttpStatus.CONFLICT
            );
          }

          //const password = Math.random().toString(36).substring(2, 8);
          const password = "12345678";
          const encryptedPassword = await bcrypt.hash(password, 10);

          profile = await ProfilesModel.create(
            {
              email: emailAddress,
              new_password: encryptedPassword,
              pro_pic: this.defaultProfilePic,
              is_blacklisted: 0,
              mobile_verified: 0,
              email_verified: 0,
              total_asset_amount: 0.0,
              monthly_living_cost: 0.0,
              monthly_loan_installment_amount: 0.0,
              created_by: loginUser?.business_member_id,
              created_by_name: loginUser?.name,
              updated_by_name: loginUser?.name,
              created_at: Date.now(),
            },
            { transaction }
          );

          const member = await MembersModel.create(
            {
              profile_id: profile.id,
              is_blacklisted: 0,
              mobile_verified: 0,
              email_verified: 0,
              total_asset_amount: 0.0,
              monthly_living_cost: 0.0,
              monthly_loan_installment_amount: 0.0,
              created_by: 0,
              created_by_name: loginUser?.name,
              updated_by_name: loginUser?.name,
              updated_by: loginUser.business_member_id,
              created_at: Date.now(),
            },
            { transaction }
          );

          const businessMember = await BusinessMembersModel.create(
            {
              business_id: loginUser?.business_id,
              member_id: member.id,
              status: EMPLOYEE_STATUS.INVITED,
              is_verified: 0,
              created_by: 0,
              created_by_name: loginUser?.name,
              updated_by_name: loginUser?.name,
              updated_by: 0,
              created_at: Date.now(),
            },
            { transaction }
          );

          if (businessMember) {
            await BusinessMemberStatusChangeLogModel.create(
              {
                business_member_id: businessMember.id,
                from_status: EMPLOYEE_STATUS.INVITED,
                to_status: EMPLOYEE_STATUS.INVITED,
                log: `Super Admin invited ${emailAddress}`,
                created_by: loginUser?.business_member_id,
                created_by_name: `Member-${loginUser.name}`,
                created_at: new Date(),
              },
              { transaction }
            );
          }

          const apiResponse = await axios.post(
            `${AUTH_API_URL}/auth-api/mail/v1/send-invitation`,
            {
              email: emailAddress,
              company_name: "digiGO",
              message: "Invitation from your co-worker to join digiGO",
              password: password,
            }
          );
          if (apiResponse.status !== 201) {
            throw new HttpException(
              `Failed to send invitation to ${emailAddress}`,
              HttpStatus.BAD_REQUEST
            );
          }
        }

        await transaction.commit();

        return successResponse({}, "Invitations processed", HttpStatus.CREATED);
      } catch (error) {
        await transaction.rollback();
        if (
          error.name === "SequelizeLockWaitTimeoutError" &&
          attempt < maxRetries - 1
        ) {
          attempt++;
          console.log(`Retrying transaction... Attempt ${attempt + 1}`);
        } else {
          ErrorLog("Employee Invitation", "employee", error);
          return errorResponse(
            error?.message || "Failed to invite member",
            error?.response?.data || {},
            error?.status || HttpStatus.INTERNAL_SERVER_ERROR
          );
        }
      }
    }
  }

  async reinviteEmail(
    reinviteEmailDto: ReinviteEmailDto,
    businessMemberId: number,
    loginUser: any
  ): Promise<any> {
    const { email } = reinviteEmailDto;
    const transaction = await this.sequelize.transaction();
    try {
      const profile = await ProfilesModel.findOne({
        where: { email },
        transaction,
      });

      if (!profile) {
        throw new HttpException("Email not found", HttpStatus.NOT_FOUND);
      }

      const businessMember = await BusinessMembersModel.findOne({
        where: {
          id: businessMemberId,
          status: EMPLOYEE_STATUS.INVITED,
        },
        transaction,
      });

      if (!businessMember) {
        throw new HttpException(
          "No invitation found for this email",
          HttpStatus.NOT_FOUND
        );
      }
      const apiResponse = await axios.post(
        `${AUTH_API_URL}/auth-api/mail/v1/send-invitation`,
        {
          email: email,
          company_name: "digiGO",
          message: "Invitation from your co-worker to join digiGO",
          password: "12345678",
        }
      );
      if (apiResponse.status !== 201) {
        throw new HttpException(
          `Failed to send invitation to ${email}`,
          HttpStatus.BAD_REQUEST
        );
      }
      return successResponse({}, "Success", HttpStatus.OK);
    } catch (error) {
      await transaction.rollback();
      ErrorLog("Re-invite Email", "employee", error);
      return errorResponse(
        error?.message || "Failed to re-invite member",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private getIncludeConfigfindAll(
    search_type: string,
    search: string,
    business_department_id: number,
    departmentIds?: number[]
  ) {
    return [
      {
        model: BusinessesModel,
        attributes: ["id", "name"],
      },
      {
        model: MembersModel,
        required: search != "" && search != null && search != undefined,
        attributes: ["id", "social_links"], // Select specific attributes from MembersModel
        include: [
          {
            model: ProfilesModel,
            required: search != "" && search != null && search != undefined,
            attributes: ["id", "name", "email", "mobile", "pro_pic"],
            ...(search_type === "name" || search_type === "email"
              ? {
                  where: {
                    [search_type]: {
                      [Op.like]: `%${search}%`,
                    },
                  },
                }
              : {}),
          },
        ],
      },
      {
        model: BusinessRolesModel,
        required: !!(
          business_department_id ||
          (departmentIds && departmentIds.length)
        ),
        attributes: ["id", "name"], // Select specific attributes from BusinessRolesModel
        where: {
          ...(departmentIds && departmentIds.length
            ? { business_department_id: { [Op.in]: departmentIds } }
            : business_department_id
              ? { business_department_id: business_department_id }
              : {}),
        },
        include: [
          {
            model: BusinessDepartmentsModel,
            attributes: ["id", "name"],
          },
        ],
      },
      {
        model: BusinessMemberBkashInfoModel,
        attributes: ["id", "business_member_id", "account_no"],
      },
      {
        model: SalariesModel,
        attributes: ["gross_salary"],
        required: false,
      },
    ];
  }

  async findAll(
    skip: number,
    limit: number,
    business_department_id: number,
    business_role_id: number,
    status = "",
    employee_type = "",
    search = "",
    search_type = "",
    manager_id: number,
    loginUser: any,
    is_all: boolean,
    is_wifi_network_active?: number,
    is_location_active?: number,
    is_remote_active?: number,
    departmentIds?: number[],
    is_in_attendance_device?: boolean
  ) {
    try {
      const whereCondition: any = {};
      if (
        is_in_attendance_device === true ||
        is_in_attendance_device === false
      ) {
        whereCondition.is_in_attendance_device = is_in_attendance_device;
      }
      if (business_role_id) {
        whereCondition.business_role_id = business_role_id;
      }
      if (status) {
        whereCondition.status = status;
      }
      if (employee_type) {
        whereCondition.employee_type = employee_type;
      }
      if (search_type == "employee_id") {
        if (search != "" && search != null && search != undefined) {
          whereCondition.employee_id = {
            [Op.like]: `%${search}%`,
          };
        }
      }
      if (manager_id) {
        whereCondition.manager_id = manager_id;
      }
      if (!isNaN(is_wifi_network_active)) {
        whereCondition.is_wifi_network_active = is_wifi_network_active;
      }
      if (!isNaN(is_location_active)) {
        whereCondition.is_location_active = is_location_active;
      }
      if (!isNaN(is_remote_active)) {
        whereCondition.is_remote_active = is_remote_active;
      }
      whereCondition.business_id = loginUser?.business_id;

      const includeConfig = this.getIncludeConfigfindAll(
        search_type,
        search,
        business_department_id,
        departmentIds
      );

      console.log(includeConfig);

      const queryOptions: any = {
        where: whereCondition,
        include: includeConfig,
        order: [["id", "DESC"]],
      };

      // Only add limit/offset if not requesting all
      if (!is_all) {
        queryOptions.limit = limit;
        queryOptions.offset = skip;
      }
      console.log("query", queryOptions);
      const employees =
        await BusinessMembersModel.findAndCountAll(queryOptions);

      // Transform response to add payroll_show_alert
      const transformedEmployees = employees.rows.map((employee) => {
        const salaryInfo = employee.get("salary");
        return {
          ...employee.get(),
          payroll_show_alert: !(salaryInfo && salaryInfo.gross_salary > 0),
        };
      });

      return successResponse(
        {
          totalLength: employees.count,
          currentLength: transformedEmployees.length,
          employees: transformedEmployees,
        },
        "Employee fetched successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("employee find", "employee-find", error);
      return errorResponse(
        error?.message || "Failed to find employees",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async pdfGenerate(id: number, loginUser: any) {
    console.log(loginUser);
    try {
      const employee = await BusinessMembersModel.findByPk(id, {
        include: [
          {
            model: BusinessesModel,
            attributes: ["id", "name", "logo"],
            where: {
              id: loginUser?.business_id,
            },
            // include: [
            // 	{
            // 		model: BusinessMemberAdditionalSectionsModel,
            // 		attributes: ["id", "name"],
            // 	}
            // ]
          },
          {
            model: MembersModel,
            attributes: [
              "id",
              "emergency_contract_person_name",
              "emergency_contract_person_number",
              "emergency_contract_person_relationship",
              "social_links",
            ], // Select specific attributes from MembersModel
            include: [
              {
                model: ProfilesModel,
                attributes: [
                  "id",
                  "name",
                  "email",
                  "gender",
                  "pro_pic",
                  "dob",
                  "mobile",
                  "address",
                  "nationality",
                  "nid_no",
                  "nid_image_front",
                  "nid_image_back",
                  "passport_no",
                  "passport_image",
                  "blood_group",
                  "fb_id",
                  "tin_no",
                  "tin_certificate",
                ],
                include: [
                  {
                    model: ProfileBankInformationsModel,
                    attributes: ["id", "bank_name", "account_no"],
                  },
                ],
              },
            ],
          },
          {
            model: BusinessRolesModel,
            attributes: ["id", "name"], // Select specific attributes from BusinessRolesModel
            include: [
              {
                model: BusinessDepartmentsModel,
                attributes: ["id", "name"],
              },
            ],
          },
          {
            model: BusinessMemberAdditionalDataModel,
            attributes: ["id", "business_member_id", "field_id", "value"],
            include: [
              {
                model: BusinessMemberAdditionalFieldsModel,
                attributes: ["id", "name", "label", "type", "rules"],
                include: [
                  {
                    model: BusinessMemberAdditionalSectionsModel,
                    attributes: ["id", "name"],
                  },
                ],
              },
            ],
          },
          {
            model: BusinessMemberBkashInfoModel,
            attributes: ["id", "business_member_id", "account_no"],
          },
          {
            model: AppreciationModel,
            as: "receivedAppreciations",
            include: [
              {
                model: StickerModel,
                attributes: ["id", "image"],
              },
              {
                model: BusinessMembersModel,
                as: "giver",
                attributes: ["id"],
                include: [
                  {
                    model: MembersModel,
                    attributes: ["id"],
                    include: [
                      {
                        model: ProfilesModel,
                        attributes: ["name", "pro_pic"],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: BusinessMembersModel,
            as: "manager",
            attributes: ["id"],
            include: [
              {
                model: MembersModel,
                attributes: ["id"],
                include: [
                  {
                    model: ProfilesModel,
                    attributes: ["id", "name"],
                  },
                ],
              },
            ],
          },
        ],
      });
      if (!employee) {
        throw new HttpException("Employee not found", HttpStatus.NOT_FOUND);
      }

      const lastInactiveStatus =
        await BusinessMemberStatusChangeLogModel.findOne({
          where: {
            business_member_id: id,
            to_status: EMPLOYEE_STATUS.INACTIVE,
          },
          order: [["created_at", "DESC"]],
          attributes: ["created_at"],
        });

      // Format data for template
      const templateData = {
        // Basic Info
        name: employee.member?.profile?.name || "N/A",
        email: employee.member?.profile?.email || "N/A",
        department: employee.role?.department?.name || "N/A",
        designation: employee.role?.name || "N/A",
        manager: employee.manager?.member?.profile?.name || "N/A",
        join_date: employee.join_date
          ? moment(employee.join_date).format("DD.MM.YY")
          : "N/A",
        deactivation_date: lastInactiveStatus?.created_at
          ? moment(lastInactiveStatus.created_at).format("DD.MM.YY")
          : "N/A",
        employee_type: employee.employee_type || "N/A",
        employee_id: employee.employee_id || "N/A",
        grade: employee.grade || "N/A",
        profile_image: employee.member?.profile?.pro_pic,
        logo: employee.business?.logo || null,
        company_name: employee.business?.name || "",

        // Personal Info
        personal_info: {
          gender: employee.member?.profile?.gender || "N/A",
          phone: employee?.mobile || "N/A",
          dob: employee.member?.profile?.dob
            ? moment(employee.member?.profile?.dob).format("DD.MM.YY")
            : "N/A",
          address: employee.member?.profile?.address || "N/A",
          nationality: employee.member?.profile?.nationality || "N/A",
          nid: employee.member?.profile?.nid_no || "N/A",
          passport: employee.member?.profile?.passport_no || "N/A",
          blood_group: employee.member?.profile?.blood_group || "N/A",
        },

        // Financial Info
        financial_info: {
          tin: employee.member?.profile?.tin_no || "N/A",
          bank_name: employee.member?.profile?.bank_info?.bank_name || "N/A",
          bank_account:
            employee.member?.profile?.bank_info?.account_no || "N/A",
          bkash_account: employee.bkash_info?.account_no || "N/A",
        },

        // Emergency Contact (from additional data)
        emergency_contacts: [
          {
            label: "Name",
            value: employee.member?.emergency_contract_person_name || "N/A",
          },
          {
            label: "Number",
            value: employee.member?.emergency_contract_person_number || "N/A",
          },
          {
            label: "Relationship",
            value:
              employee.member?.emergency_contract_person_relationship || "N/A",
          },
        ],
      };

      const pdfBuffer = await this.generatePdf(templateData);

      return pdfBuffer;
    } catch (error) {
      ErrorLog("employee find", "employee-find", error);
      return errorResponse(
        error?.message || "Failed to find employee",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async compileTemplate() {
    const filePath = path.join(process.cwd(), "views", "co_worker_details.hbs");
    console.log("Template file path:", filePath);
    const template = fs.readFileSync(filePath, "utf8");
    return hbs.compile(template);
  }

  async generatePdf(data: any): Promise<Buffer> {
    hbs.registerHelper("toWords", (number) => {
      return toWords(number);
    });

    hbs.registerHelper("formatDate", (date, format) => {
      return moment(date).format(format);
    });
    try {
      const compile = await this.compileTemplate();
      const html = compile({
        ...data,
        currentDate: new Date(), // Pass current date
      });

      const pdfOptions = {
        format: "A4",
        border: "10mm",
        base: `file://${process.cwd()}/views/`,
        localUrlAccess: true,
      };

      return new Promise((resolve, reject) => {
        pdf.create(html, pdfOptions).toBuffer((err, buffer) => {
          if (err) {
            console.error("❌ PDF Generation Error:", err);
            reject(new InternalServerErrorException("Failed to generate PDF"));
          }
          resolve(buffer);
        });
      });
    } catch (error) {
      console.error("❌ PDF Error:", error);
      throw new InternalServerErrorException("PDF Generation Failed");
    }
  }

  async findOne(id: number, loginUser: any) {
    console.log(loginUser);
    try {
      const employee = await BusinessMembersModel.findByPk(id, {
        include: [
          {
            model: BusinessesModel,
            attributes: ["id", "name", "is_enable_employee_visit"],
            where: {
              id: loginUser?.business_id,
            },
            // include: [
            // 	{
            // 		model: BusinessMemberAdditionalSectionsModel,
            // 		attributes: ["id", "name"],
            // 	}
            // ]
          },
          {
            model: MembersModel,
            attributes: [
              "id",
              "emergency_contract_person_name",
              "emergency_contract_person_number",
              "emergency_contract_person_relationship",
              "social_links",
            ], // Select specific attributes from MembersModel
            include: [
              {
                model: ProfilesModel,
                attributes: [
                  "id",
                  "name",
                  "email",
                  "gender",
                  "pro_pic",
                  "dob",
                  "mobile",
                  "address",
                  "nationality",
                  "nid_no",
                  "email_verified_at",
                  "email_verified",
                  "nid_image_front",
                  "nid_image_back",
                  "passport_no",
                  "passport_image",
                  "blood_group",
                  "fb_id",
                  "tin_no",
                  "tin_certificate",
                ],
                include: [
                  {
                    model: ProfileBankInformationsModel,
                    attributes: ["id", "bank_name", "account_no"],
                  },
                ],
              },
            ],
          },
          {
            model: BusinessRolesModel,
            attributes: ["id", "name"], // Select specific attributes from BusinessRolesModel
            include: [
              {
                model: BusinessDepartmentsModel,
                attributes: ["id", "name"],
              },
            ],
          },
          {
            model: BusinessMemberAdditionalDataModel,
            attributes: ["id", "business_member_id", "field_id", "value"],
            include: [
              {
                model: BusinessMemberAdditionalFieldsModel,
                attributes: ["id", "name", "label", "type", "rules"],
                include: [
                  {
                    model: BusinessMemberAdditionalSectionsModel,
                    attributes: ["id", "name"],
                  },
                ],
              },
            ],
          },
          {
            model: BusinessMemberBkashInfoModel,
            attributes: ["id", "business_member_id", "account_no"],
          },
          {
            model: AppreciationModel,
            as: "receivedAppreciations",
            include: [
              {
                model: StickerModel,
                attributes: ["id", "image"],
              },
              {
                model: BusinessMembersModel,
                as: "giver",
                attributes: ["id"],
                include: [
                  {
                    model: MembersModel,
                    attributes: ["id"],
                    include: [
                      {
                        model: ProfilesModel,
                        attributes: ["name", "pro_pic"],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            model: BusinessMembersModel,
            as: "manager",
            attributes: ["id"],
            include: [
              {
                model: MembersModel,
                attributes: ["id"],
                include: [
                  {
                    model: ProfilesModel,
                    attributes: ["id", "name"],
                  },
                ],
              },
            ],
          },
        ],
      });
      if (!employee) {
        throw new HttpException("Employee not found", HttpStatus.NOT_FOUND);
      }
      return successResponse(
        { employee: employee },
        "Employee details found successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("employee find", "employee-find", error);
      return errorResponse(
        error?.message || "Failed to find employee",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateOfficialData(
    id: number,
    updateEmployeeOfficialDataDto: updateEmployeeOfficialDataDto,
    loginUser: any
  ) {
    const transaction = await this.sequelize.transaction();
    try {
      const business_member = await BusinessMembersModel.findByPk(id);
      if (!business_member) {
        throw new HttpException(
          "Official data not found",
          HttpStatus.NOT_FOUND
        );
      }

      // Check for duplicate employee_id
      if (updateEmployeeOfficialDataDto?.employee_id) {
        const existingEmployeeId = await BusinessMembersModel.findOne({
          where: {
            employee_id: updateEmployeeOfficialDataDto.employee_id,
            id: { [Op.ne]: id }, // Exclude current employee
            business_id: loginUser.business_id,
          },
        });

        if (existingEmployeeId) {
          throw new HttpException(
            "Employee ID already exists",
            HttpStatus.CONFLICT
          );
        }
      }
      business_member.business_role_id =
        updateEmployeeOfficialDataDto.business_role_id;
      business_member.join_date = updateEmployeeOfficialDataDto.join_date;
      business_member.work_station = updateEmployeeOfficialDataDto.work_station;
			business_member.expense_unit = updateEmployeeOfficialDataDto.expense_unit;
      business_member.manager_id = updateEmployeeOfficialDataDto.manager_id
        ? updateEmployeeOfficialDataDto.manager_id
        : null;
      business_member.employee_id = updateEmployeeOfficialDataDto.employee_id;
      business_member.grade = updateEmployeeOfficialDataDto.grade;
      (business_member.employee_type =
        updateEmployeeOfficialDataDto.employee_type),
        (business_member.updated_by_name = `Member-${loginUser.name}`);
      business_member.updated_by = loginUser.business_member_id;
      business_member.updated_at = new Date(); // Use SQL to set the current timestamp
      business_member.rfid = updateEmployeeOfficialDataDto.rfid;
      await business_member.save();
      if (business_member) {
        // Update line manager information
        if (
          updateEmployeeOfficialDataDto.manager_id != null &&
          updateEmployeeOfficialDataDto.manager_id !=
            updateEmployeeOfficialDataDto.old_manager_id
        ) {
          const manager = await BusinessMembersModel.findByPk(
            updateEmployeeOfficialDataDto.manager_id
          );
          const userType = manager?.type.toLowerCase();
          if (userType != BUSINESS_MEMBER_TYPE.ADMIN) {
            manager.type = BUSINESS_MEMBER_TYPE.MANAGER;
            manager.updated_by_name = `Member-${loginUser.name}`;
            manager.updated_by = loginUser.business_member_id;
            manager.updated_at = new Date(); // Use SQL to set the current timestamp
            await manager.save();
          }
        }
        if (
          updateEmployeeOfficialDataDto.old_manager_id != null &&
          updateEmployeeOfficialDataDto.manager_id !=
            updateEmployeeOfficialDataDto.old_manager_id
        ) {
          const checkSubstitudeEmployee = await BusinessMembersModel.findOne({
            where: {
              manager_id: updateEmployeeOfficialDataDto.old_manager_id,
            },
          });
          if (!checkSubstitudeEmployee) {
            const oldManager = await BusinessMembersModel.findByPk(
              updateEmployeeOfficialDataDto.old_manager_id
            );
            const userType = oldManager?.type.toLowerCase();
            if (userType != BUSINESS_MEMBER_TYPE.ADMIN) {
              oldManager.type = BUSINESS_MEMBER_TYPE.EMPLOYEE;
              oldManager.updated_by_name = `Member-${loginUser.name}`;
              oldManager.updated_by = loginUser.business_member_id;
              oldManager.updated_at = new Date(); // Use SQL to set the current timestamp
              await oldManager.save();
            }
          }
        }

        const profile = await ProfilesModel.findByPk(
          updateEmployeeOfficialDataDto.profile_id
        );
        profile.name = updateEmployeeOfficialDataDto.name;
        profile.email = updateEmployeeOfficialDataDto.email;
        profile.pro_pic = updateEmployeeOfficialDataDto.pro_pic
          ? updateEmployeeOfficialDataDto.pro_pic
          : profile.pro_pic || this.defaultProfilePic;
        profile.updated_by_name = `Member-${loginUser.name}`;
        profile.updated_by = loginUser.business_member_id;
        profile.updated_at = new Date(); // Use SQL to set the current timestamp
        await profile.save();

        if (
          updateEmployeeOfficialDataDto.name &&
          business_member.is_in_attendance_device
        ) {
          await this.attendanceDeviceService.createEmployeeToDevice({
            identifier: `${business_member.id}`,
            name: updateEmployeeOfficialDataDto.name,
            primary_display_text: "Welcome to digiGO",
            rfid: updateEmployeeOfficialDataDto?.rfid
              ? `${updateEmployeeOfficialDataDto.rfid}`
              : "",
            secondary_display_text: updateEmployeeOfficialDataDto.name,
          });
        } else if (!business_member.is_in_attendance_device) {
          const res = await this.attendanceDeviceService.createEmployeeToDevice(
            {
              identifier: `${business_member.id}`,
              name: updateEmployeeOfficialDataDto.name,
              primary_display_text: "Welcome to digiGO",
              rfid: updateEmployeeOfficialDataDto?.rfid
                ? `${updateEmployeeOfficialDataDto.rfid}`
                : "",
              secondary_display_text: updateEmployeeOfficialDataDto.name,
            }
          );

          if (res.issuccess === true) {
            await BusinessMembersModel.update(
              {
                is_in_attendance_device: true,
              },
              {
                where: {
                  id: business_member.id,
                },
              }
            );
          }
        }
      }

      return successResponse(
        {},
        "Re-invitation sent and status updated",
        HttpStatus.OK
      );
    } catch (error) {
      await transaction.rollback();
      ErrorLog("Re-invite Email", "employee", error);
      return errorResponse(
        error?.message || "Failed to re-invite member",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  async create(
    createEmployeeDto: CreateEmployeeDto,
    loginUser: any,
    headers: any
  ) {
    try {
      // Start transaction
      let businessMember;
      let password;
      const result = await ProfilesModel.sequelize.transaction(async (t) => {
        // Check if email already exists
        const existingProfile = await ProfilesModel.findOne({
          where: { email: createEmployeeDto.email },
          transaction: t,
        });

        if (existingProfile) {
          throw new HttpException("Email already exists", HttpStatus.CONFLICT);
        }

        // const password = Math.random().toString(36).substring(2, 8);
        password = "12345678";
        const encryptedPassword = await bcrypt.hash(password, 10);
        // Create profile
        const profile = await ProfilesModel.create(
          {
            name: createEmployeeDto.name,
            email: createEmployeeDto.email,
            new_password: encryptedPassword,
            gender: createEmployeeDto.gender,
            pro_pic: this.defaultProfilePic,
            created_by: loginUser.business_member_id,
            created_by_name: `Member-${loginUser.name}`,
            created_at: new Date(),
            updated_by_name: `Member-${loginUser.name}`,
            updated_by: loginUser.business_member_id,
          },
          { transaction: t }
        );

        // Create member
        const member = await MembersModel.create(
          {
            profile_id: profile.id,
            created_by: loginUser.business_member_id,
            created_by_name: `Member-${loginUser.name}`,
            updated_by_name: `Member-${loginUser.name}`,
            updated_by: loginUser.business_member_id,
            created_at: new Date(),
          },
          { transaction: t }
        );

        // Create business member
        businessMember = await BusinessMembersModel.create(
          {
            business_id: loginUser?.business_id, // Set appropriate business ID
            member_id: member.id,
            business_role_id: createEmployeeDto.business_role_id,
            join_date: createEmployeeDto.join_date,
            department: createEmployeeDto.department,
            type: BUSINESS_MEMBER_TYPE.EMPLOYEE,
            employee_type: EMPLOYEE_TYPE.PERMANENT,
            status: EMPLOYEE_STATUS.ACTIVE,
            created_by: loginUser.business_member_id,
            created_by_name: `Member-${loginUser.name}`,
            updated_by_name: `Member-${loginUser.name}`,
            updated_by: loginUser.business_member_id,
            created_at: new Date(),
          },
          { transaction: t }
        );
      });

      // assign employee to attendance device
      if (businessMember && businessMember.id) {
        const response =
          await this.attendanceDeviceService.createEmployeeToDevice({
            identifier: businessMember.id,
            name: createEmployeeDto.name,
            rfid: "",
            primary_display_text: "Welcome",
            secondary_display_text: createEmployeeDto.name,
          });

        if (response.issuccess === true) {
          await BusinessMembersModel.update(
            {
              is_in_attendance_device: true,
            },
            {
              where: {
                id: businessMember.id,
              },
            }
          );
        }
      }

      // Create shift assignments
      const startDate = new Date();
      const endDate = subDays(addMonths(startDate, 3), 1); // Decrement 1 day from the end date
      if (businessMember.id) {
        await this.createShiftAssignments(
          businessMember.id,
          startDate.toISOString(),
          endDate.toISOString(),
          loginUser,
          headers
        );
      }

      try {
        const salary = createEmployeeDto?.salary || 0;

        // Call payroll API
        await axios.post(
          `${PAYROLL_API_URL}/payroll-api/payroll-setting/v1/employee-component-setting`,
          {
            business_member_id: businessMember.id,
            gross_salary: salary,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: headers?.authorization,
              productid: headers?.productid,
            },
          }
        );
      } catch (error) {
        ErrorLog("Payroll API call", "employee-create", error);
        // Don't throw error, just log it since this is not critical
      }
      try {
				const apiResponse = await axios.post(
					`${AUTH_API_URL}/auth-api/mail/v1/send-invitation`,
						{
							email: createEmployeeDto.email,
							company_name: "digiGO",
							message: "Invitation from your co-worker to join digiGO",
							password: password,
						}
					);
					if (apiResponse.status !== 201) {
						throw new HttpException(
							`Failed to send invitation to ${createEmployeeDto.email}`,
							HttpStatus.BAD_REQUEST
						);
				}
			} catch (error) {
				ErrorLog("Auth mail API call", "employee-create", error);
				// Don't throw error, just log it since this is not critical
			}

      return successResponse(
        result,
        "Employee created successfully",
        HttpStatus.CREATED
      );
    } catch (error) {
      ErrorLog("employee create", "employee-create", error);
      return errorResponse(
        error?.message || "Failed to create employee",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async activateInvitedEmployee(
    createEmployeeDto: ActivateInviteEmployeeDto,
    businessMemberId: number,
    loginUser: any,
    headers: any
  ): Promise<any> {
    const transaction = await this.sequelize.transaction();
    try {
      // Find the business member
      const businessMember = await BusinessMembersModel.findOne({
        where: {
          id: businessMemberId,
          status: EMPLOYEE_STATUS.INVITED,
        },
        include: [
          {
            model: MembersModel,
            include: [
              {
                model: ProfilesModel,
              },
            ],
          },
        ],
        transaction,
      });

      if (!businessMember) {
        throw new HttpException(
          "Invited employee not found or already activated",
          HttpStatus.NOT_FOUND
        );
      }

      // Update business member data
      businessMember.status = EMPLOYEE_STATUS.ACTIVE;
      businessMember.business_role_id = createEmployeeDto.business_role_id;
      businessMember.join_date = createEmployeeDto.join_date;
      businessMember.updated_by = loginUser.business_member_id;
      businessMember.department = createEmployeeDto.department;
      businessMember.updated_by_name = loginUser.name;
      businessMember.updated_at = new Date();
      await businessMember.save({ transaction });

      // Update profile data
      const profile = businessMember.member.profile;
      profile.name = createEmployeeDto.name;
      profile.gender = createEmployeeDto.gender as GENDER;
      profile.updated_by = loginUser.business_member_id;
      profile.updated_by_name = loginUser.name;
      profile.updated_at = new Date();
      await profile.save({ transaction });

      // Create status change log
      await BusinessMemberStatusChangeLogModel.create(
        {
          business_member_id: businessMember.id,
          from_status: EMPLOYEE_STATUS.INVITED,
          to_status: EMPLOYEE_STATUS.ACTIVE,
          log: `Employee activated by ${loginUser.name}`,
          created_by: loginUser.business_member_id,
          created_by_name: loginUser.name,
          created_at: new Date(),
        },
        { transaction }
      );

      await transaction.commit();

      // Create shift assignments
      const startDate = new Date();
      const endDate = subDays(addMonths(startDate, 3), 1); // Decrement 1 day from the end date
      if (businessMember.id) {
        await this.createShiftAssignments(
          businessMember.id,
          startDate.toISOString(),
          endDate.toISOString(),
          loginUser,
          headers
        );
      }

      try {
        const salary = createEmployeeDto?.gross_salary || 0;

        // Call payroll API
        await axios.post(
          `${PAYROLL_API_URL}/payroll-api/payroll-setting/v1/employee-component-setting`,
          {
            business_member_id: businessMember.id,
            gross_salary: salary,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: headers?.authorization,
              productid: headers?.productid,
            },
          }
        );
      } catch (error) {
        ErrorLog("Payroll API call", "employee-create", error);
        // Don't throw error, just log it since this is not critical
      }

      return successResponse(
        { businessMember },
        "Employee activated successfully",
        HttpStatus.OK
      );
    } catch (error) {
      await transaction.rollback();
      ErrorLog("Employee", "activate-invited-employee", error);
      return errorResponse(
        error?.message || "Failed to activate employee",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async removeBusinessMember(
    businessMemberId: number,
    loginUser: any
  ): Promise<any> {
    const transaction = await this.sequelize.transaction();
    try {
      const businessMember = await BusinessMembersModel.findByPk(
        businessMemberId,
        { transaction }
      );

      if (!businessMember) {
        throw new HttpException(
          "Business member not found",
          HttpStatus.NOT_FOUND
        );
      }

      if (businessMember.status !== EMPLOYEE_STATUS.INVITED) {
        throw new HttpException("User can't remove", HttpStatus.BAD_REQUEST);
      }

      const member = await MembersModel.findByPk(businessMember.member_id, {
        transaction,
      });
      const profile = await ProfilesModel.findByPk(member.profile_id, {
        transaction,
      });

      await businessMember.destroy({ transaction });
      await member.destroy({ transaction });
      await profile.destroy({ transaction });

      await transaction.commit();

      return successResponse(
        {},
        "Business member and dependencies removed successfully",
        HttpStatus.OK
      );
    } catch (error) {
      await transaction.rollback();
      ErrorLog("Remove Business Member", "employee", error);
      return errorResponse(
        error?.message || "Failed to remove business member",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updatePersonalData(
    id: number,
    updateEmployeePersonalDataDto: updateEmployeePersonalDataDto,
    loginUser: any
  ) {
    const transaction = await this.sequelize.transaction();
    try {
      const business_member = await BusinessMembersModel.findByPk(id);
      if (!business_member) {
        throw new HttpException(
          "Personal data not found",
          HttpStatus.NOT_FOUND
        );
      }

      // Check if mobile number exists for other business members
      if (updateEmployeePersonalDataDto?.mobile) {
        const existingMobileNumber = await BusinessMembersModel.findOne({
          where: {
            mobile: updateEmployeePersonalDataDto.mobile,
            id: { [Op.ne]: id }, // Exclude current business member
            business_id: loginUser.business_id, // Check within same business
          },
        });

        if (existingMobileNumber) {
          throw new HttpException(
            "Mobile number already exist",
            HttpStatus.CONFLICT
          );
        }
      }

      business_member.mobile = updateEmployeePersonalDataDto?.mobile;
      business_member.updated_by_name = `Member-${loginUser.name}`;
      business_member.updated_by = loginUser.business_member_id;
      business_member.updated_at = new Date(); // Use SQL to set the current timestamp
      await business_member.save(); // update business_member table
      if (business_member) {
        const member = await MembersModel.findByPk(
          updateEmployeePersonalDataDto.member_id
        );
        (member.social_links = updateEmployeePersonalDataDto.social_links),
          (member.updated_by_name = `Member-${loginUser.name}`);
        member.updated_by = loginUser.business_member_id;
        member.updated_at = new Date(); // Use SQL to set the current timestamp
        await member.save(); // update members table

        // Profile data update
        const profile = await ProfilesModel.findByPk(
          updateEmployeePersonalDataDto.profile_id
        );
        profile.gender = updateEmployeePersonalDataDto.gender;
        profile.dob = updateEmployeePersonalDataDto.dob;
        profile.address = updateEmployeePersonalDataDto.address;
        profile.nationality = updateEmployeePersonalDataDto.nationality;
        profile.nid_no = updateEmployeePersonalDataDto.nid_no;
        profile.nid_image_front = updateEmployeePersonalDataDto.nid_image_front;
        profile.nid_image_back = updateEmployeePersonalDataDto.nid_image_back;
        profile.passport_no = updateEmployeePersonalDataDto.passport_no;
        profile.passport_image = updateEmployeePersonalDataDto.passport_image;
        profile.blood_group = updateEmployeePersonalDataDto.blood_group;
        //profile.fb_id = updateEmployeePersonalDataDto.fb_id;
        profile.updated_by_name = `Member-${loginUser.name}`;
        profile.updated_by = loginUser.business_member_id;
        profile.updated_at = new Date(); // Use SQL to set the current timestamp
        await profile.save(); // update profiles table
      }

      return successResponse(
        {},
        "Personal data updated successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Employee personal information", "personal-data", error);
      await transaction.rollback();
      return errorResponse(
        error?.message || "Failed to update personal data",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateFinancialData(
    id: number,
    updateEmployeeFinancialDataDto: updateEmployeeFinancialDataDto,
    loginUser: any
  ) {
    const transaction = await this.sequelize.transaction();
    try {
      // Employee profile tin info update
      const profile = await ProfilesModel.findByPk(
        updateEmployeeFinancialDataDto.profile_id
      );
      profile.tin_no = updateEmployeeFinancialDataDto?.tin_no;
      profile.tin_certificate = updateEmployeeFinancialDataDto?.tin_certificate;
      profile.updated_by_name = `Member-${loginUser.name}`;
      profile.updated_by = loginUser?.business_member_id;
      profile.updated_at = new Date(); // Use SQL to set the current timestamp
      await profile.save(); // update profiles table

      // Employee Bkash Information
      const baksh_info = await BusinessMemberBkashInfoModel.findOne({
        where: {
          business_member_id: updateEmployeeFinancialDataDto.business_member_id,
        },
      });
      if (baksh_info) {
        if (updateEmployeeFinancialDataDto?.account_no) {
          const existingBkashAccount =
            await BusinessMemberBkashInfoModel.findOne({
              where: {
                account_no: updateEmployeeFinancialDataDto?.account_no,
                business_member_id: {
                  [Op.ne]: updateEmployeeFinancialDataDto.business_member_id,
                },
              },
            });

          if (existingBkashAccount) {
            throw new HttpException(
              `bKash account number ${updateEmployeeFinancialDataDto?.account_no} is already in use by another employee`,
              HttpStatus.CONFLICT
            );
          }
          baksh_info.account_no = updateEmployeeFinancialDataDto?.account_no;
          baksh_info.updated_by_name = `Member-${loginUser.name}`;
          baksh_info.updated_by = loginUser?.business_member_id;
          baksh_info.updated_at = new Date(); // Use SQL to set the current timestamp
          await baksh_info.save(); // update business_member_bkash_info table
        }
      } else {
        if (updateEmployeeFinancialDataDto?.account_no) {
          const existingBkashAccount =
            await BusinessMemberBkashInfoModel.findOne({
              where: {
                account_no: updateEmployeeFinancialDataDto?.account_no,
              },
            });
          if (existingBkashAccount) {
            throw new HttpException(
              `bKash account number ${updateEmployeeFinancialDataDto?.account_no} is already in use by another employee`,
              HttpStatus.CONFLICT
            );
          }
          const bkashData = {
            business_member_id:
              updateEmployeeFinancialDataDto.business_member_id,
            account_no: updateEmployeeFinancialDataDto?.account_no,
            created_by: loginUser?.business_member_id,
            created_by_name: `Member-${loginUser.name}`,
            updated_by_name: `Member-${loginUser.name}`,
            created_at: Date.now(),
          };
          console.log("bkashData:: ", bkashData);
          //Create the business_member_bkash_info in the database
          const newBkashInfo = await BusinessMemberBkashInfoModel.create(
            bkashData,
            {
              validate: true,
            }
          );
        }
      }

      // Employee Bank information
      const bank_info = await ProfileBankInformationsModel.findOne({
        where: {
          profile_id: updateEmployeeFinancialDataDto.profile_id,
        },
      });
      if (bank_info) {
        if (updateEmployeeFinancialDataDto?.bank_name != undefined) {
          bank_info.bank_name = updateEmployeeFinancialDataDto.bank_name;
        }
        if (updateEmployeeFinancialDataDto?.bank_account != undefined) {
          bank_info.account_no = updateEmployeeFinancialDataDto.bank_account;
        }
        bank_info.updated_by_name = `Member-${loginUser.name}`;
        bank_info.updated_by = loginUser?.business_member_id;
        bank_info.updated_at = new Date(); // Use SQL to set the current timestamp
        await bank_info.save(); // update profile_bank_informations table
      } else {
        const bankData = {
          profile_id: updateEmployeeFinancialDataDto.profile_id,
          bank_name: updateEmployeeFinancialDataDto?.bank_name || null,
          account_no: updateEmployeeFinancialDataDto?.bank_account || null,
          created_by: loginUser?.business_member_id,
          created_by_name: `Member-${loginUser.name}`,
          updated_by_name: `Member-${loginUser.name}`,
          created_at: Date.now(),
        };

        //Create the profile_bank_informations in the database
        const newBankInfo = await ProfileBankInformationsModel.create(
          bankData,
          {
            validate: true,
          }
        );
      }

      return successResponse(
        {},
        "Financial data updated successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Employee financial information", "financial-data", error);
      await transaction.rollback();
      return errorResponse(
        error?.message || "Failed to update financial data",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateEmergencyData(
    id: number,
    updateEmployeeEmergencyContactDataDto: updateEmployeeEmergencyContactDataDto,
    loginUser: any
  ) {
    try {
      const member = await MembersModel.findByPk(
        updateEmployeeEmergencyContactDataDto.member_id
      );
      if (!member) {
        throw new HttpException("Employee not found", HttpStatus.NOT_FOUND);
      }

      member.emergency_contract_person_name =
        updateEmployeeEmergencyContactDataDto?.emergency_contract_person_name;
      member.emergency_contract_person_number =
        updateEmployeeEmergencyContactDataDto?.emergency_contract_person_number;
      member.emergency_contract_person_relationship =
        updateEmployeeEmergencyContactDataDto?.emergency_contract_person_relationship;
      member.updated_by_name = `Member-${loginUser.name}`;
      member.updated_by = loginUser?.business_member_id;
      member.updated_at = new Date();
      await member.save();

      return successResponse(
        {},
        "Employee emergency contact info updated successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Employee status", "financial-data", error);
      return errorResponse(
        error?.message || "Failed to update emergency contact data",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateAdditionalData(
    id: number,
    updateEmployeeAdditionalDataDto: updateEmployeeAdditionalDataDto,
    loginUser: any
  ) {
    try {
      const existingAdditionalData =
        await BusinessMemberAdditionalDataModel.findAll({
          where: { business_member_id: id },
        });

      const incomingDataIds =
        updateEmployeeAdditionalDataDto.additional_data.map(
          (data) => data.additional_data_id
        );

      // Delete records that are not in the incoming data
      for (const existingData of existingAdditionalData) {
        if (!incomingDataIds.includes(existingData.id)) {
          await existingData.destroy();
        }
      }

      for (const data of updateEmployeeAdditionalDataDto.additional_data) {
        if (data.additional_data_id != 0) {
          const additionalData =
            await BusinessMemberAdditionalDataModel.findByPk(
              data.additional_data_id
            );
          if (additionalData) {
            additionalData.value = data.value;
            additionalData.updated_by_name = loginUser?.name;
            additionalData.updated_by = loginUser?.business_member_id;
            additionalData.updated_at = new Date();
            await additionalData.save();
          }
        } else {
          if (data.value && data.value != "") {
            const existingData =
              await BusinessMemberAdditionalDataModel.findOne({
                where: {
                  business_member_id: id,
                  field_id: data.field_id,
                },
              });

            if (existingData) {
              existingData.value = data.value;
              existingData.updated_by_name = loginUser?.name;
              existingData.updated_by = loginUser?.business_member_id;
              existingData.updated_at = new Date();
              await existingData.save();
            } else {
              const newAdditionalData = {
                business_member_id: id,
                field_id: data.field_id,
                value: data.value,
                created_by: loginUser?.business_member_id,
                created_by_name: loginUser?.name,
                updated_by: loginUser?.business_member_id,
                updated_by_name: `Member-${loginUser.name}`,
                created_at: new Date(),
              };
              await BusinessMemberAdditionalDataModel.create(
                newAdditionalData,
                {
                  validate: true,
                }
              );
            }
          }
        }
      }

      return successResponse(
        {},
        "Employee additional data updated successfully",
        HttpStatus.OK
      );
    } catch (error) {
      console.log("error", error);
      ErrorLog("Employee additional data", "additional-data", error);
      return errorResponse(
        error?.message || "Failed to update additional data",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  async employeeStatusUpdate(id: number, status: string, loginUser: any) {
    try {
      await this.updateEmployeeStatus(id, status, loginUser);

      return successResponse(
        {},
        "Employee status updated successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Employee status", "employee-status", error);
      return errorResponse(
        error?.message || "Failed to update employee status",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private async updateEmployeeStatus(
    id: number,
    status: string,
    loginUser: any
  ): Promise<boolean> {
    const employee = await BusinessMembersModel.findByPk(id);
    if (!employee) {
      throw new HttpException("Employee not found", HttpStatus.NOT_FOUND);
    }

    const current_status = employee.status;

    if (status == EMPLOYEE_STATUS.ACTIVE) {
      employee.status = EMPLOYEE_STATUS.ACTIVE;
    } else if (status == EMPLOYEE_STATUS.INACTIVE) {
      employee.status = EMPLOYEE_STATUS.INACTIVE;
      employee.inactived_at = new Date();
    }

    employee.updated_by = loginUser?.business_member_id;
    employee.updated_by_name = `Member-${loginUser.name}`;
    const updateStatus = await employee.save();

    if (updateStatus) {
      await BusinessMemberStatusChangeLogModel.create({
        business_member_id: id,
        from_status: current_status,
        to_status: status,
        log: `Employee status changed from ${current_status} to ${status}`,
        created_by: loginUser?.business_member_id,
        created_by_name: `Member-${loginUser.name}`,
        created_at: new Date(),
      });
    }

    return !!updateStatus;
  }

  async employeeStatusBulkUpdate(
    employeeStatusBulkUpdateDto: EmployeeStatusBulkUpdateDto,
    loginUser: any
  ) {
    try {
      for (const id of employeeStatusBulkUpdateDto.employee_id) {
        await this.updateEmployeeStatus(
          id,
          employeeStatusBulkUpdateDto.status,
          loginUser
        );
      }

      return successResponse(
        {},
        "Employee status updated successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Employee status", "employee-status", error);
      return errorResponse(
        error?.message || "Failed to update employee status",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private async createShiftAssignments(
    businessMemberId: number,
    startDate: string,
    endDate: string,
    loginUser: any,
    headers: any
  ) {
    try {
      // calling holiday service to get the holiday betweeen start_date & end_date
      const holidayResponse = await axios.get(
        `${HOLIDAY_API_URL}/holiday-api/business-holiday/v1/get-holiday-by-date-to-date`,
        {
          params: {
            start_date: format(new Date(startDate), "yyyy-MM-dd"),
            end_date: format(new Date(endDate), "yyyy-MM-dd"),
          },
          headers: {
            "Content-Type": "application/json",
            Authorization: headers?.authorization,
            productid: headers?.productid,
          },
        }
      );

      // Format holidays for shift API payload
      const formattedHolidays =
        holidayResponse.data?.data?.holidays?.reduce((acc, holiday) => {
          const startDate = new Date(holiday.start_date);
          const endDate = new Date(holiday.end_date);
          const dates = [];

          // Loop through all dates between start and end (inclusive)
          for (
            let date = startDate;
            date <= endDate;
            date.setDate(date.getDate() + 1)
          ) {
            dates.push({
              date: format(new Date(date), "yyyy-MM-dd"),
              title: holiday.title,
            });
          }

          return [...acc, ...dates];
        }, []) || [];

      // Prepare request body for shift API
      const requestBody = {
        business_member_id: [businessMemberId],
        repeat: 1,
        repeat_type: "days",
        repeat_range: 1,
        start_date: format(new Date(startDate), "yyyy-MM-dd"),
        end_date: format(new Date(endDate), "yyyy-MM-dd"),
        days: [],
        holidays: formattedHolidays,
        shift_settings: {
          days: [],
          repeat: 1,
          end_date: format(new Date(endDate), "yyyy-MM-dd"),
          repeat_type: "days",
          repeat_range: 1,
        },
      };

      // Make API call to shift service
      const response = await axios.post(
        `${SHIFT_API_URL}/shift-api/shift-assign/v1/assign-general-attendance`,
        requestBody,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: headers?.authorization,
            productid: headers?.productid,
          },
        }
      );

      if (response.status !== 201) {
        throw new Error("Failed to create shift assignments");
      }
    } catch (error) {
      throw error;
    }
  }

  remove(id: number) {
    return `This action removes a #${id} employee`;
  }

  async fileUploader(fileBuffer: Buffer, loginUser: any): Promise<any> {
    try {
      if (!fileBuffer) {
        throw new Error("File buffer is missing");
      }
      const getUserDataInJson = this.fileUploadService.parseExcel(fileBuffer);
      for (const userData of getUserDataInJson) {
        const email = userData["Employee Email"];
        const accountNo = userData["bKash Number"];
        // Check if profile exists

        const profile = await ProfilesModel.findOne({
          where: { email },
        });
        if (!profile) {
          throw new HttpException(
            `Profile with email ${email} not found`,
            HttpStatus.NOT_FOUND
          );
        }

        // Check if member exists
        const member = await MembersModel.findOne({
          where: { profile_id: profile.id },
        });
        if (!member) {
          throw new HttpException(
            `Member with profile ID ${profile.id} not found`,
            HttpStatus.NOT_FOUND
          );
        }

        // Check if business member exists
        const businessMember = await BusinessMembersModel.findOne({
          where: {
            member_id: member.id,
            business_id: loginUser?.business_id,
          },
        });
        if (!businessMember) {
          throw new HttpException(
            `Employee with email ${email} not found in your business`,
            HttpStatus.NOT_FOUND
          );
        }

        // Check if business member bkash info exists
        let businessMemberBkashInfo =
          await BusinessMemberBkashInfoModel.findOne({
            where: { business_member_id: businessMember.id },
          });

        if (businessMemberBkashInfo) {
          // Update account_no
          const existingBkashAccount =
            await BusinessMemberBkashInfoModel.findOne({
              where: {
                account_no: accountNo,
                business_member_id: {
                  [Op.ne]: businessMember.id,
                },
              },
            });

          if (existingBkashAccount) {
            throw new HttpException(
              `bKash account number ${accountNo} is already in use by another employee`,
              HttpStatus.CONFLICT
            );
          }
          businessMemberBkashInfo.account_no = accountNo;
          await businessMemberBkashInfo.save();
        } else {
          // Create new business member bkash info
          const existingBkashAccount =
            await BusinessMemberBkashInfoModel.findOne({
              where: { account_no: accountNo },
            });

          if (existingBkashAccount) {
            throw new HttpException(
              `bKash account number ${accountNo} is already in use by another employee`,
              HttpStatus.CONFLICT
            );
          }
          businessMemberBkashInfo = await BusinessMemberBkashInfoModel.create({
            business_member_id: businessMember.id,
            account_no: accountNo,
            created_by: member.id,
            created_by_name: profile.name,
            created_at: new Date(),
            updated_by_name: profile.name,
            updated_at: new Date(),
          });
        }
      }
      // Commit transaction

      return {
        message: "Employee bkash account data has been updated successfully",
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateProfilePicture(id: number, proPic: string, loginUser: any) {
    try {
      const businessMember = await BusinessMembersModel.findByPk(id);
      if (!businessMember) {
        throw new HttpException(
          "Business member not found",
          HttpStatus.NOT_FOUND
        );
      }

      const member = await MembersModel.findOne({
        where: { id: businessMember.member_id },
      });

      if (!member) {
        throw new HttpException("Member not found", HttpStatus.NOT_FOUND);
      }

      const profile = await ProfilesModel.findByPk(member.profile_id);
      if (!profile) {
        throw new HttpException("Profile not found", HttpStatus.NOT_FOUND);
      }

      // Update only the pro_pic field
      profile.pro_pic = proPic;
      profile.updated_by = loginUser?.business_member_id;
      profile.updated_by_name = `Member-${loginUser.name}`;
      profile.updated_at = new Date();
      await profile.save();

      // download image from url and send to attendance device
      if (businessMember.is_in_attendance_device) {
        try {
          const parsedUrl = new URL(proPic);
          const isValidUrl =
            parsedUrl.protocol === "http:" || parsedUrl.protocol === "https:";

          if (isValidUrl) {
            const response = await axios.get(proPic, {
              responseType: "arraybuffer",
            });
            const imageBuffer = Buffer.from(response.data, "binary");

            await this.attendanceDeviceService.createEmployeeToDevice({
              identifier: businessMember.id.toString(),
              name: profile.name,
              primary_display_text: "Welcome",
              secondary_display_text: profile.name,
              image: imageBuffer,
              rfid: businessMember?.rfid || "",
              response: response,
            });
          }
        } catch (error) {
          console.error("Invalid URL:", proPic);
        }
      }

      return successResponse(
        {},
        "Profile picture updated successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Profile picture update", "profile-picture", error);
      return errorResponse(
        error?.message || "Failed to update profile picture",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getBusinessDetails(businessId: number) {
    try {
      const business = await BusinessesModel.findOne({
        where: { id: businessId },
      });

      if (!business) {
        throw new HttpException("Business not found", HttpStatus.NOT_FOUND);
      }
      business.employee_size = await BusinessMembersModel.count({
        where: {
          business_id: businessId,
          status: EMPLOYEE_STATUS.ACTIVE,
        },
      });
      await business.save();

      return successResponse(
        { business },
        "Business details fetched successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Business details", "business-details", error);
      return errorResponse(
        error?.message || "Failed to fetch business details",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getRecentBusinessMembers(
    loginUser: any,
    business_department_id?: number
  ): Promise<any> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const whereClause: any = {
        join_date: {
          [Op.gte]: thirtyDaysAgo,
        },
        status: EMPLOYEE_STATUS.ACTIVE,
        business_id: loginUser.business_id,
      };

      if (business_department_id) {
        whereClause["$role.business_department_id$"] = business_department_id;
      }

      const new_employees = await BusinessMembersModel.findAll({
        where: whereClause,
        include: [
          {
            model: MembersModel,
            include: [
              {
                model: ProfilesModel,
                attributes: ["id", "name", "pro_pic"],
              },
            ],
            attributes: ["id"],
          },
          {
            model: BusinessRolesModel,
            as: "role",
            attributes: ["business_department_id"],
            include: [
              {
                model: BusinessDepartmentsModel,
                attributes: ["name"],
              },
            ],
          },
        ],
        attributes: ["id"],
      });

      const transformedEmployees = new_employees.map((employee) => ({
        business_member_id: employee.id,
        name: employee.member?.profile?.name || "",
        pro_pic: employee.member?.profile?.pro_pic || "",
      }));

      return successResponse(
        { new_employees: transformedEmployees },
        "Business details fetched successfully",
        HttpStatus.OK
      );
    } catch (error) {
      return errorResponse(
        error?.message || "Failed to fetch business details",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getYearBasedData(year: number, businessId: number): Promise<any> {
    try {
      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;
      console.log("bujsienss Id : ", businessId);
      const query = `
		   SELECT
			 COUNT(bm.id) AS totalEmployees,
			 SUM(CASE WHEN p.gender = 'male' THEN 1 ELSE 0 END) AS maleEmployees,
			 SUM(CASE WHEN p.gender = 'female' THEN 1 ELSE 0 END) AS femaleEmployees,
			 SUM(CASE WHEN bm.join_date BETWEEN :startDate AND :endDate THEN 1 ELSE 0 END) AS onboardedEmployees,
			 SUM(CASE WHEN bm.employee_type = :permanent THEN 1 ELSE 0 END) AS fullTimeEmployees,
			 SUM(CASE WHEN bm.employee_type = :onProbation THEN 1 ELSE 0 END) AS provisionalEmployees
		   FROM
			 business_member bm
		   JOIN
			 members m ON bm.member_id = m.id
		   JOIN
			 profiles p ON m.profile_id = p.id
		   WHERE
			 bm.join_date <= :endDate
			 AND bm.status = :activeStatus
			 AND bm.business_id = :businessId
		 `;

      const results = await this.sequelize.query(query, {
        replacements: {
          startDate,
          endDate,
          permanent: EMPLOYEE_TYPE.PERMANENT,
          onProbation: EMPLOYEE_TYPE.ON_PROBATION,
          activeStatus: EMPLOYEE_STATUS.ACTIVE,
          businessId,
        },
        type: QueryTypes.SELECT,
      });

      const data: any = results[0];

      return successResponse({
        totalEmployees: Number(data.totalEmployees),
        maleEmployees: Number(data.maleEmployees),
        femaleEmployees: Number(data.femaleEmployees),
        onboardedEmployees: Number(data.onboardedEmployees),
        fullTimeEmployees: Number(data.fullTimeEmployees),
        provisionalEmployees: Number(data.provisionalEmployees),
      });
    } catch (error) {
      console.log("error : ", error);
      throw new HttpException(
        "Failed to fetch year-based data",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getEmployeeList(loginUser: any) {
    try {
      const employees = await BusinessMembersModel.findAll({
        where: {
          business_id: loginUser.business_id,
        },
        include: [
          {
            model: MembersModel,
            attributes: [
              "emergency_contract_person_number",
              "emergency_contract_person_relationship",
              "emergency_contract_person_name",
            ],
            include: [
              {
                model: ProfilesModel,
                attributes: [
                  "name",
                  "email",
                  "dob",
                  "address",
                  "nationality",
                  "nid_no",
                  "tin_no",
                ],
                include: [
                  {
                    model: ProfileBankInformationsModel,
                    attributes: ["bank_name", "account_no"], // Bank details
                  },
                ],
              },
            ],
          },
          {
            model: BusinessRolesModel,
            attributes: ["name"], // Designation
            include: [
              {
                model: BusinessDepartmentsModel,
                attributes: ["name"], // Department
              },
            ],
          },
          {
            model: BusinessMemberBkashInfoModel,
            attributes: ["account_no"], // bKash account
          },
        ],
      });

      const transformedEmployeesPromises = employees.map(async (employee) => {
        const managerInfo = await BusinessMembersModel.findOne({
          where: {
            id: employee.manager_id,
          },
          include: [
            {
              model: MembersModel,
              attributes: ["id"],
              include: [
                {
                  model: ProfilesModel,
                  attributes: ["name", "email"],
                },
              ],
            },
          ],
        });

        return {
          employee_id: employee.employee_id || null,
          name: employee.member?.profile?.name || null,
          phone: employee?.mobile || null,
          email: employee.member?.profile?.email || null,
          status: employee.status || null,
          department: employee.role?.department?.name || null,
          designation: employee.role?.name || null,
          manager: employee.manager_id
            ? managerInfo?.member?.profile?.name
            : null,
          joining_date: employee.join_date || null,
          grade: employee.grade || null,
          employee_type: employee.type || null,
          dob: employee.member?.profile?.dob || null,
          address: employee.member?.profile?.address || null,
          nationality: employee.member?.profile?.nationality || null,
          nid_passport: employee.member?.profile?.nid_no || null,
          tin: employee.member?.profile?.tin_no || null,
          bank_name: employee.member?.profile?.bank_info?.bank_name || null,
          bank_account_no:
            employee.member?.profile?.bank_info?.account_no || null,
          bkash_account_no: employee.bkash_info?.account_no || null,
          emergency_contact: {
            number: employee.member?.emergency_contract_person_number || null,
            relationship:
              employee.member?.emergency_contract_person_relationship || null,
            name: employee.member?.emergency_contract_person_name || null,
          },
        };
      });

      const transformedEmployees = await Promise.all(
        transformedEmployeesPromises
      );

      return successResponse(
        {
          employees: transformedEmployees,
        },
        "Employee list fetched successfully",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Employee list fetch", "employee-list-fetch", error);
      return errorResponse(
        error?.message || "Failed to fetch employee list",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateMemberTypes(updateMemberTypesDto: UpdateMemberTypesDto) {
    const { Sheet1 } = updateMemberTypesDto;

    for (const entry of Sheet1) {
      const { Emails, Type } = entry;

      // Find the profile by email
      const profile = await ProfilesModel.findOne({ where: { email: Emails } });
      if (profile) {
        // Find the member associated with the profile
        const member = await MembersModel.findOne({
          where: { profile_id: profile.id },
        });
        if (member) {
          // Find the business member associated with the member
          const businessMember = await BusinessMembersModel.findOne({
            where: { status: EMPLOYEE_STATUS.ACTIVE, member_id: member.id },
          });
          if (businessMember) {
            // Update the type based on the provided Type
            businessMember.type = Type.toUpperCase() as BUSINESS_MEMBER_TYPE;
            const zz = await businessMember.save();
          }
        }
      }
    }
  }

  async updateMemberTypesWithID(
    updateMemberTypesIDDto: UpdateMemberTypesIDDto
  ) {
    // Find the business member associated with the member
    const businessMember = await BusinessMembersModel.findOne({
      where: { id: updateMemberTypesIDDto.business_memebr_id },
    });
    if (businessMember) {
      // Update the type based on the provided Type
      businessMember.type =
        updateMemberTypesIDDto.type.toUpperCase() as BUSINESS_MEMBER_TYPE;
      const zz = await businessMember.save();
    }
  }

  async updateManagerTypes() {
    try {
      // Fetch all unique manager_id values
      const uniqueManagerIds = await BusinessMembersModel.findAll({
        attributes: [
          [Sequelize.fn("DISTINCT", Sequelize.col("manager_id")), "manager_id"],
        ],
        where: {
          manager_id: {
            [Op.ne]: null,
          },
        },
      });

      // Update type to Manager for each unique manager_id
      for (const manager of uniqueManagerIds) {
        await BusinessMembersModel.update(
          { type: BUSINESS_MEMBER_TYPE.MANAGER },
          { where: { id: manager.manager_id } }
        );
      }

      console.log("Manager types updated successfully");
    } catch (error) {
      console.error("Error updating manager types:", error);
    }
  }

  async updateNullTypesToEmployee() {
    try {
      await BusinessMembersModel.update(
        { type: BUSINESS_MEMBER_TYPE.EMPLOYEE },
        {
          where: {
            type: {
              [Op.is]: null,
            },
          },
        }
      );

      return successResponse(
        {},
        "Successfully updated null types to EMPLOYEE",
        HttpStatus.OK
      );
    } catch (error) {
      ErrorLog("Update null types", "employee-type-update", error);
      return errorResponse(
        error?.message || "Failed to update employee types",
        error?.response?.data || {},
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
