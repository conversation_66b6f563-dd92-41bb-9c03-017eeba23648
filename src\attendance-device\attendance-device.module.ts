import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ScheduleModule } from "@nestjs/schedule";
import { AuthService } from "src/auth/auth.service";
import { AttendanceDeviceController } from "./attendance-device.controller";
import { AttendanceDeviceService } from "./attendance-device.service";

@Module({
  imports: [HttpModule, ScheduleModule.forRoot()],
  controllers: [AttendanceDeviceController],
  providers: [AttendanceDeviceService, AuthService],
  exports: [AttendanceDeviceService],
})
export class AttendanceDeviceModule {}
