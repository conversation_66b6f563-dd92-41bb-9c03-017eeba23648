import { IsNotEmpty, IsOptional, IsString, IsInt, IsDate } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateDepartmentDto {
  @IsNotEmpty()
  @IsInt()
  @IsOptional()
  business_id: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  abbreviation?: string;

  @IsOptional()
  @IsString()
  logo?: string;

  @IsOptional()
  @IsString()
  icon?: string;

  @IsNotEmpty()
  @IsInt()
  @IsOptional()
  is_published: number;

  @IsNotEmpty()
  @IsInt()
  @IsOptional()
  created_by: number;

  @IsOptional()
  @IsString()
  @IsOptional()
  created_by_name?: string;

  @IsNotEmpty()
  @IsInt()
  @IsOptional()
  updated_by: number;

  @IsOptional()
  @IsString()
  updated_by_name?: string;
}