import { Module } from '@nestjs/common';
import { DepartmentService } from "./department.service";
import { DepartmentController } from "./department.controller";
import { DatabaseModule } from "../config/database/database.module";
import { AuthService } from "src/auth/auth.service";

@Module({
    imports: [DatabaseModule],
    controllers: [DepartmentController],
    providers: [DepartmentService, AuthService],
})
export class DepartmentModule {}
