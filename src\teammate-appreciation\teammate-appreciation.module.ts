import { Module } from '@nestjs/common';
import { TeammateAppreciationService } from './teammate-appreciation.service';
import { TeammateAppreciationController } from './teammate-appreciation.controller';
import {AuthService} from "../auth/auth.service"
@Module({
  controllers: [TeammateAppreciationController],
  providers: [TeammateAppreciationService,AuthService],
})
export class TeammateAppreciationModule {}
