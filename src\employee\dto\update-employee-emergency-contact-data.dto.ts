import {
    IsString,
    IsNotEmpty,
    IsOptional,   
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class updateEmployeeEmergencyContactDataDto {
    @IsString()
    @IsOptional()
    emergency_contract_person_name: string | null;

    @IsString()
    @IsOptional()
    emergency_contract_person_number: string | null;

    @IsString()
    @IsOptional()
    emergency_contract_person_relationship: string | null;

    @IsNotEmpty()
    member_id: number;
}
