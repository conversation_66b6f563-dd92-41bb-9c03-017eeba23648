import {
    Controller,
    Post,
    Get,
    Delete,
    Body,
    Param,
    Query,
    Patch,
    Req,
    UseGuards,
    Res,
} from "@nestjs/common";
import { RecruitmentManagementService } from "./recruitment_management.service";
import { CreateJobPostDto } from "./dto/create-recruitment_management.dto";
import { UpdateJobPostDto } from "./dto/update-recruitment_management.dto";
import { ServiceTokenGuard } from "src/guards";
import { Response } from "express";
import { CreateJobApplicationDto } from "./dto/create-job-application.dto";

@Controller("recruitment-management")
export class RecruitmentManagementController {
    constructor(private readonly recruitmentManagementService: RecruitmentManagementService) {}

    // @UseGuards(ServiceTokenGuard)
    @Post("v1/job-post/create")
    async createRecruitmentManagement(
        @Req() req,
        @Body() createJobPostDto: CreateJobPostDto,
        @Res() res: Response
    ) {
        let data = await this.recruitmentManagementService.create(
            createJobPostDto,
        );
        return res.status(data.statusCode).json(data);
    }

    // @UseGuards(ServiceTokenGuard)
    @Get("v1/job-post/details/:id")
    async getRecruitmentManagement(
        @Req() req,
        @Param("id") id: number,
        @Res() res: Response
    ) {
        let data = await this.recruitmentManagementService.findOne(id);
        return res.status(data.statusCode).json(data);
    }

    // @UseGuards(ServiceTokenGuard)
    @Patch("v1/job-post/update/:id")
    async updateRecruitmentManagement(
        @Req() req,
        @Param("id") id: number,
        @Body()  updateJobPostDto: UpdateJobPostDto,
        @Res() res: Response
    ) {
        let data = await this.recruitmentManagementService.update(
            id,
            updateJobPostDto,
        );
        return res.status(data.statusCode).json(data);
    }

    // @UseGuards(ServiceTokenGuard)
    @Patch("v1/job-posts/status-update/:id")
    async recruitmentManagementStatusUpdate(
        @Req() req,
        @Param("id") id: string,
        @Body("status") status: string,
        @Res() res: Response
    ) {
        let data = await this.recruitmentManagementService.recruitmentManagementStatusUpdate(
            +id,
            status,
        );
        return res.status(data.statusCode).json(data);
    }

    // @UseGuards(ServiceTokenGuard)
    @Delete("v1/job-post/delete/:id")
    async deleteRecruitmentManagement(@Param("id") id: number, @Res() res: Response) {
        let data = await this.recruitmentManagementService.remove(id);
        return res.status(data.statusCode).json(data);
    }

    // @UseGuards(ServiceTokenGuard)
    @Get("v1/job-posts/data")
    async findAll(
        @Req() req,
        @Query("skip") skip: string,
        @Query("limit") limit: string,
        @Query("search") search: string,
        @Query("business_id") business_id: string,
        @Query("status") status: string,
        @Res() res: Response
    ) {
        let is_all = false;
        if ((skip == "0" && limit == "0") || skip == undefined || limit == undefined) {
            is_all = true;
        }
        const skipNumber = parseInt(skip, 10) || 0;
        const limitNumber = parseInt(limit, 10) || 10;
        const businessIdNumber = business_id ? parseInt(business_id, 10) : undefined;
        let data = await this.recruitmentManagementService.findAll(
            skipNumber,
            limitNumber,
            is_all,
            search,
            businessIdNumber,
            status
        );
        return res.status(data.statusCode).json(data);
    }


    // job application part ============================

    @Post('v1/job-application/create')
    async createJobApplication(
        @Body() createJobApplicationDto: CreateJobApplicationDto,
        @Res() res: Response
    ) {
        const data = await this.recruitmentManagementService.createJobApplication(createJobApplicationDto);
        return res.status(data.statusCode).json(data);
    }

    @Get('v1/job-applications/data')
    async getAllJobApplications(
        @Query("skip") skip: string,
        @Query("limit") limit: string,
        @Query("business_id") business_id: string,
        @Query("job_post_id") job_post_id: string,
        @Res() res: Response
    ) {
        let is_all = false;
        if ((skip == "0" && limit == "0") || skip == undefined || limit == undefined) {
            is_all = true;
        }
        const skipNumber = parseInt(skip, 10) || 0;
        const limitNumber = parseInt(limit, 10) || 10;
        const businessIdNumber = business_id ? parseInt(business_id, 10) : undefined;
        const jobPostIdNumber = job_post_id ? parseInt(job_post_id, 10) : undefined;
        const data = await this.recruitmentManagementService.getAllJobApplications(
            skipNumber,
            limitNumber,
            is_all,
            businessIdNumber,
            jobPostIdNumber,
        );
        return res.status(data.statusCode).json(data);
    }

    // GET: Single application by id
    @Get('v1/job-application/details/:id')
    async getJobApplication(
        @Param('id') id: string,
        @Res() res: Response
    ) {
        const data = await this.recruitmentManagementService.getJobApplication(Number(id));
        return res.status(data.statusCode).json(data);
    }
}
