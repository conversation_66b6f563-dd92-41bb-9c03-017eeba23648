import { Table, Column, Model, DataType, HasMany } from 'sequelize-typescript';
import { StickerModel } from './sticker.model';

@Table({
    tableName: 'sticker_categories',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
})
export class StickerCategoryModel extends Model<StickerCategoryModel> {
    @Column({
        type: DataType.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    })
    id: number;

    @Column({
        type: DataType.STRING,
        allowNull: false,
    })
    name: string;

    @Column({
        type: DataType.STRING,
        allowNull: false,
    })
    title: string;

    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    created_by: number;

    @Column({
        type: DataType.STRING,
        allowNull: true,
    })
    created_by_name: string;

    @Column({
        type: DataType.INTEGER,
        allowNull: false,
    })
    updated_by: number;

    @Column({
        type: DataType.STRING,
        allowNull: true,
    })
    updated_by_name: string;

    @Column({
        type: DataType.DATE,
        allowNull: false,
    })
    created_at: Date;

    @Column({
        type: DataType.DATE,
        allowNull: false,
    })
    updated_at: Date;

    @HasMany(() => StickerModel)
    stickers: StickerModel[];
}