import {
  <PERSON>ongsTo,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  HasOne,
  Model,
  Table,
} from "sequelize-typescript";
import { AppreciationModel } from "./appreciation.model";
import { BusinessesModel } from "./Business.model";
import { BusinessMemberAdditionalDataModel } from "./BusinessMemberAdditionalData.model";
import { BusinessMemberBkashInfoModel } from "./BusinessMemberBkashInfo.model";
import { BusinessRolesModel } from "./BusinessRole.model";
import { MembersModel } from "./Member.model";

import {
  BUSINESS_MEMBER_TYPE,
  EMPLOYEE_STATUS,
  EMPLOYEE_TYPE,
  LIVE_TRACK_CONFIGURATION,
  PAYROLL_CONFIGURATION,
  SHIFT_CONFIGURATION,
} from "../constants/api.enums";
import { AttendanceDeviceEnrollmentModel } from "./AttendanceDeviceEnrollment.model";
import { SalariesModel } from "./salary.model";
import { User<PERSON>ttendanceDeviceModel } from "./UserAttendanceDevice.model";

// @Table({ tableName: 'business_member', timestamps: true })
@Table({ tableName: "business_member" })
export class BusinessMembersModel extends Model {
  @Column({
    type: DataType.BIGINT,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => BusinessesModel)
  @Column({
    // Primary key of businesses table
    type: DataType.INTEGER,
    allowNull: true,
  })
  business_id: number;

  @ForeignKey(() => MembersModel)
  @Column({
    // Primary key of members table
    type: DataType.INTEGER,
    allowNull: false,
  })
  member_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  employee_id: string;

  @Column({
    // Primary key of business_member table
    type: DataType.INTEGER,
    allowNull: true,
  })
  manager_id: number;

  @Column({
    type: DataType.STRING,
    //enum: BUSINESS_MEMBER_TYPE,
    //defaultValue: BUSINESS_MEMBER_TYPE.ADMIN,
    allowNull: true,
  })
  type: BUSINESS_MEMBER_TYPE;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  join_date: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  previous_institution: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  designation: string;

  @Column({
    type: DataType.STRING,
    //enum: EMPLOYEE_TYPE,
    defaultValue: EMPLOYEE_TYPE.PERMANENT,
    allowNull: false,
  })
  employee_type: EMPLOYEE_TYPE;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  department: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mobile: string;

  @ForeignKey(() => BusinessRolesModel)
  @Column({
    // Primary key of business_roles table
    type: DataType.INTEGER,
    allowNull: true,
  })
  business_role_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  grade: string;

  @Column({
		type: DataType.STRING,
		allowNull: true,
	})
	expense_unit : string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	work_station: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0, // value is 0,1
    allowNull: false,
  })
  is_verified: number;

  @Column({
    type: DataType.STRING,
    //enum: EMPLOYEE_STATUS,
    defaultValue: EMPLOYEE_STATUS.ACTIVE,
    allowNull: false,
  })
  status: EMPLOYEE_STATUS;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 1, // value is 0,1
    allowNull: false,
  })
  is_super: number;

  @Column({
    type: DataType.INTEGER,
    //enum: PAYROLL_CONFIGURATION,
    defaultValue: PAYROLL_CONFIGURATION.DISABLE,
    allowNull: false,
  })
  is_payroll_enable: PAYROLL_CONFIGURATION;

  @Column({
    type: DataType.INTEGER,
    //enum: LIVE_TRACK_CONFIGURATION,
    defaultValue: LIVE_TRACK_CONFIGURATION.DISABLE,
    allowNull: false,
  })
  is_live_track_accessible: LIVE_TRACK_CONFIGURATION;

  @Column({
    type: DataType.INTEGER,
    //enum: LIVE_TRACK_CONFIGURATION,
    defaultValue: LIVE_TRACK_CONFIGURATION.DISABLE,
    allowNull: false,
  })
  is_live_track_enable: LIVE_TRACK_CONFIGURATION;

  @Column({
    type: DataType.INTEGER,
    //enum: SHIFT_CONFIGURATION,
    defaultValue: SHIFT_CONFIGURATION.DISABLE,
    allowNull: false,
  })
  is_shift_enable: SHIFT_CONFIGURATION;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false,
  })
  early_bird_counter: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false,
  })
  is_location_active: number; // 0 or 1

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false,
  })
  is_wifi_network_active: number; // 0 or 1

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false,
  })
  is_remote_active: number; // 0 or 1

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false,
  })
  late_loteef_counter: number;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: true,
  })
  deleted_at: Date;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false,
  })
  created_by: number; // Profiles table primary key

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  created_by_name: string; // Profiles table name

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    //allowNull: false
    allowNull: true,
  })
  updated_by: number; // Profiles table primary key

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  updated_by_name: string; // Profiles table name

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: false,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: true, // maybe allowNull: true
  })
  inactived_at: Date;

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: true, // maybe allowNull: true
  })
  updated_at: Date;

  @BelongsTo(() => MembersModel)
  member: MembersModel;

  @BelongsTo(() => BusinessesModel)
  business: BusinessesModel;

  @BelongsTo(() => BusinessRolesModel)
  role: BusinessRolesModel;

  @HasOne(() => BusinessMemberBkashInfoModel)
  bkash_info: BusinessMemberBkashInfoModel;

  @HasMany(() => BusinessMemberAdditionalDataModel)
  additional_data: BusinessMemberAdditionalDataModel[];

  // Relation for Appreciation
  @HasMany(() => AppreciationModel, "receiver_id")
  receivedAppreciations: AppreciationModel[];

  @HasMany(() => AppreciationModel, "giver_id")
  givenAppreciations: AppreciationModel[];

  @BelongsTo(() => BusinessMembersModel, "manager_id")
  manager: BusinessMembersModel;

  @HasOne(() => BusinessMembersModel, "manager_id")
  subordinates: BusinessMembersModel;

  @HasOne(() => SalariesModel)
  salary: SalariesModel;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  is_in_attendance_device: boolean; // track user created for attendance device system

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  rfid: string; // RFID for attendance device

  @HasMany(() => AttendanceDeviceEnrollmentModel)
  attendanceDeviceEnrollments: AttendanceDeviceEnrollmentModel[];

  @HasMany(() => UserAttendanceDeviceModel)
  userAttendanceDevices: UserAttendanceDeviceModel[];
}
