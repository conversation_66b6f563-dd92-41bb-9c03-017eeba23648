import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { Op } from "sequelize";
import { ErrorLog } from "src/config/winstonLog";
import { BusinessDepartmentsModel } from "../models/BusinessDepartment.model";
import { CreateJobPostDto } from "./dto/create-recruitment_management.dto";
import { UpdateJobPostDto } from "./dto/update-recruitment_management.dto";
import { errorResponse, successResponse } from "utils/response";
import { BusinessMembersModel, BusinessRolesModel, JobApplicationModel, JobPostModel } from "src/models";
import { CreateJobApplicationDto } from "./dto/create-job-application.dto";

@Injectable()
export class RecruitmentManagementService {
    async create(createJobPostDto: CreateJobPostDto) {
        try {
            const existingJobPost = await JobPostModel.findOne({
                where: {
                    business_id: createJobPostDto.business_id,
                    job_title: createJobPostDto.job_title
                }
            });
    
            if (existingJobPost) {
                throw new HttpException(
                    'Job post with this name already exists',
                    HttpStatus.BAD_REQUEST
                );
            }
            const jobPost = await JobPostModel.create({ ...createJobPostDto });
            return successResponse(
                jobPost,
                "jobPost created successfully",
                HttpStatus.CREATED
            );
        } catch (error) {
            ErrorLog("jobPost", "jobPost-create", error);
            return errorResponse(
                error?.message || "Failed to create jobPost data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async findOne(id: number): Promise<any> {
        try {
            const jobPost = await JobPostModel.findByPk(id);
            if (!jobPost) {
                throw new HttpException(
                    "jobPost not found",
                    HttpStatus.NOT_FOUND
                );
            }
            return successResponse(
                jobPost,
                "jobPost get successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("jobPost", "jobPost-findOne", error);
            return errorResponse(
                error?.message || "Failed to fetch jobPost data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async update(
        id: number,
        updateJobPostDto: UpdateJobPostDto
    ): Promise<any> {
        try {
            if (updateJobPostDto.job_title) {
                const existingJobPost = await JobPostModel.findOne({
                    where: {
                        business_id: updateJobPostDto.business_id,
                        job_title: updateJobPostDto.job_title,
                        id: { [Op.ne]: id } // Exclude current job_title
                    }
                });
    
                if (existingJobPost) {
                    throw new HttpException(
                        'JobPost with this name already exists',
                        HttpStatus.BAD_REQUEST
                    );
                }
            }
            await JobPostModel.update(updateJobPostDto, {
                where: { id },
            });
            const jobPost = await JobPostModel.findByPk(id);
            return successResponse(
                jobPost,
                "jobPost updated successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("jobPost", "jobPost-update", error);
            return errorResponse(
                error?.message || "Failed to update jobPost data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async remove(id: number) {
        try {
            await JobPostModel.destroy({ where: { id } });
            return successResponse(
                {},
                "jobPost deleted successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("jobPost", "jobPost-delete", error);
            return errorResponse(
                error?.message || "Failed to delete jobPost data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async findAll(skip: number, limit: number, is_all: boolean, search = "", businessId?: number, status?: string) {
        try {
            const where: any = {};
            if (search) {
                where.job_title = { [Op.like]: `%${search}%` };
            }
            if (businessId) {
                where.business_id = businessId;
            }
            if (status) {
                where.status = status;
            }

            const totalCount = await JobPostModel.count({ where });
            const queryOptions: any = { where };
            if (!is_all) {
                queryOptions.limit = limit;
                queryOptions.offset = skip;
            }
            const jobPosts = await JobPostModel.findAll(queryOptions);

            return successResponse(
                {
                    totalLength: totalCount,
                    currentLength: jobPosts.length,
                    jobPosts,
                },
                "Job posts fetched successfully",
                200
            );
        } catch (error) {
            ErrorLog("Department", "department-list", error);
            return errorResponse(
                error?.message || "Failed to fetch department data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async recruitmentManagementStatusUpdate(
        id: number,
        status: string,
    ) {
        try {
            const jobPost = await JobPostModel.findByPk(id);
            if (!jobPost) {
                throw new HttpException(
                    "jobPost not found",
                    HttpStatus.NOT_FOUND
                );
            }
            await JobPostModel.update({ status }, { where: { id } });
            const updatedJobPost = await JobPostModel.findByPk(id);
            return successResponse(
                {updatedJobPost},
                "jobPost updated successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("jobPost status", "employee-status", error);
            return errorResponse(
                error?.message || "Failed to jobPost updated",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    
    async createJobApplication(dto: CreateJobApplicationDto) {
        try {
            const application = await JobApplicationModel.create({ ...dto });
            return successResponse(application, "Application submitted successfully", 201);
        } catch (error) {
            ErrorLog("JobApplication", "createJobApplication", error);
            return errorResponse(error?.message || "Failed to submit application", {}, 500);
        }
    }
    
    async getAllJobApplications(
        skip: number,
        limit: number,
        is_all: boolean,
        business_id?: number,
        job_post_id?: number,
    ) {
        try {
            let where: any = {};

            if (job_post_id) {
                where.job_post_id = job_post_id;
            } else if (business_id) {
                // Find all job posts for this business
                const jobPosts = await JobPostModel.findAll({ where: { business_id } });
                const jobPostIds = jobPosts.map(jp => jp.id);
                where.job_post_id = jobPostIds;
            }

            // Get total count
            const totalCount = await JobApplicationModel.count({ where });

            // Build query options for pagination and eager loading
            const queryOptions: any = {
                where,
            };
            if (!is_all) {
                queryOptions.offset = skip;
                queryOptions.limit = limit;
            }

            // Fetch paginated applications
            const applications = await JobApplicationModel.findAll(queryOptions);

            return successResponse(
                {
                    totalLength: totalCount,
                    currentLength: applications.length,
                    applications
                },
                "Applications fetched successfully",
                200
            );
        } catch (error) {
            ErrorLog("JobApplication", "getAllJobApplications", error);
            return errorResponse(error?.message || "Failed to fetch applications", {}, 500);
        }
    }
    
    async getJobApplication(id: number) {
        try {
            const application = await JobApplicationModel.findByPk(id);
            if (!application) throw new HttpException("Application not found", 404);
            return successResponse(application, "Application fetched successfully", 200);
        } catch (error) {
            ErrorLog("JobApplication", "getJobApplication", error);
            return errorResponse(error?.message || "Failed to fetch application", {}, 500);
        }
    }
}
