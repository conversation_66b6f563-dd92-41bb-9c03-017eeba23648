import { Table, Column, Model, DataType, ForeignKey, BelongsTo, HasMany } from 'sequelize-typescript';
import { BusinessMembersModel } from "./BusinessMember.model";
import {
  EMPLOYEE_STATUS
} from '../constants/api.enums';

// @Table({ tableName: 'business_member_status_change_logs', timestamps: true })
@Table({ tableName: 'business_member_status_change_logs' })
export class BusinessMemberStatusChangeLogModel extends Model {
  @Column({
    type: DataType.BIGINT,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => BusinessMembersModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  business_member_id: number;// Primary key of business_member table
    
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  from_status: EMPLOYEE_STATUS;
    
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  to_status: EMPLOYEE_STATUS;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  log: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false
  })
  created_by: number; // business_member table primary key
  
  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  created_by_name: string; // Profiles table name

  @Column({
    type: DataType.DATE,
    //defaultValue: DataType.NOW,
    allowNull: false
  })
  created_at: Date;

  // Relationship
  // @BelongsTo(() => BusinessMembersModel, { foreignKey: 'business_member_id', as: 'profile' })
  // profile: BusinessMembersModel;

  // @BelongsTo(() => BusinessMembersModel, { foreignKey: 'created_by', as: 'createdBy' })

    @BelongsTo(() => BusinessMembersModel)
    business_member: BusinessMembersModel;
}