import { IsNotEmpty, <PERSON>Optional, IsString, IsInt, IsObject } from 'class-validator';
import { Transform } from 'class-transformer';
export class CreateEmployeeAdditionalFieldDto {
    @IsNotEmpty()
    @IsInt()
    section_id: number;

    @IsNotEmpty()
    @IsString()
    name?: string;

    @IsNotEmpty()
    @IsString()
    label?: string;

    @IsNotEmpty()
    @IsString()
    type?: string;

    @IsOptional()
    @IsObject()
    rules: {
        extensions: string;
    }
}
