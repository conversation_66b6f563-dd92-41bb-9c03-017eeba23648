import { Injectable } from "@nestjs/common";
import * as AWS from "aws-sdk";

@Injectable()
export class DigitalOceanService {
	private readonly s3: AWS.S3;

	constructor() {
		this.s3 = new AWS.S3({
			// endpoint: new AWS.Endpoint(
			// 	"https://sheba-dev-digigo-bucket.s3.ap-southeast-1.amazonaws.com",
			// ), // Replace with your region
			region: "ap-southeast-1",
			accessKeyId: "********************",
			secretAccessKey: "Z32XmfQCqQ9H8b/A48hS1YCIfKAJgga7bxlLXokw",
		});
	}

	async uploadFile(
		file: Express.Multer.File,
		key: string,
	): Promise<AWS.S3.ManagedUpload.SendData> {
		const params = {
			Bucket: "sheba-dev-digigo-bucket",
			Key: key,
			Body: file.buffer,
			ContentType: file.mimetype,
			// ACL: "public-read", // Adjust permissions as needed
		};

		return this.s3.upload(params).promise();
	}
}
