import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

@Injectable()
export class InviteEmployeeMail {
  private transporter: nodemailer.Transporter;

  TUTORIAL_VIDEO_URL="https://youtube.com/your-tutorial-video"
  ANDROID_STORE_URL="https://play.google.com/store/apps/details?id=com.example.app"
  IOS_STORE_URL="https://apps.apple.com/app/id1234567890"

  constructor() {
    // Initialize the transporter with environment variables
    this.transporter = nodemailer.createTransport({
      //host: process.env.EMAIL_HOST,
      //port: process.env.EMAIL_PORT,
      secure: false,
      auth: {
        user: process.env.EMAIL_USERNAME,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false,
      },
    });
  }

  async sendMail(options: {
    email: string;
    company_name: string;
    message: string;
    password: string;
  }): Promise<void> {
    const { email, company_name, message, password } = options;

    const mailOptions = {
      from: process.env.SENDER_EMAIL,
      to: email,
      subject: `Invitation from your co-worker to join digiGO`,
      text: message,
      html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <style>
            body {
              font-family: Arial, sans-serif;
              color: #202124;
              line-height: 1.5;
              font-size: 14px;
            }
            .store-link {
              color: #1a73e8;
              text-decoration: none;
            }
            p {
              margin: 16px 0;
            }
            .greeting {
              font-weight: bold;
              font-size: 16px;
            }
          </style>
        </head>
        <body>
          <p class="greeting">Dear Sir/Mam,</p>

          <p>You have been invited/added to join as a co-worker. Start using digiGO by following these 2 steps:</p>

          <p>1. Download this app first here -<br>
          For Android click here: <a href="${this.ANDROID_STORE_URL}" class="store-link">Play store</a><br>
          For iOS click here: <a href="${this.IOS_STORE_URL}" class="store-link">App store</a></p>

          <p>2. Login with your account credentials-<br>
          Email: ${email}<br>
          Password: ${password} (Please change the password after login into your account)</p>

          <p>We have the video tutorials ready for you to get started, check them out here: 
          <a href="${this.TUTORIAL_VIDEO_URL}" class="store-link">Tutorial</a></p>

          <p>Regards,<br>
          Team ${company_name}</p>
        </body>
      </html>`
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`email ${email} | ${company_name} | ${message} | ${password}`)
      console.log('Email sent successfully.');
    } catch (error) {
      console.error('Error sending email:', error);
    }
  }
}
