export interface CreateDepartmentDto {
    business_id: number;
    name: string;
    abbreviation?: string;
    logo?: string;
    icon?: string;
    is_published: number;
    created_by: number;
    created_by_name?: string;
  }
  
  export interface UpdateDepartmentDto {
    name?: string;
    abbreviation?: string;
    logo?: string;
    icon?: string;
    is_published?: number;
    updated_by?: number;
    updated_by_name?: string;
  }
  
  export interface Department {
    id: number;
    business_id: number;
    name: string;
    abbreviation?: string;
    logo?: string;
    icon?: string;
    is_published: number;
    created_by: number;
    created_by_name?: string;
    updated_by?: number;
    updated_by_name?: string;
    created_at: Date;
    updated_at?: Date;
  }