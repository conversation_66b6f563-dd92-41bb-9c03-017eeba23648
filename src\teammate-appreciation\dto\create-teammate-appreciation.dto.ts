import { IsNotEmpty, <PERSON>Optional, IsString, IsInt } from 'class-validator';
import { IsDifferentId } from './is-different-id';

export class CreateTeammateAppreciationDto {
    @IsNotEmpty()
    @IsInt()
    @IsDifferentId('giver_id', { message: 'Receiver ID must not be the same as Giver ID' })
    receiver_id: number;

    @IsNotEmpty()
    @IsInt()
    giver_id: number;

    @IsNotEmpty()
    @IsInt()
    sticker_id: number;
}