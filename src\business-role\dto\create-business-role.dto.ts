import { IsNotEmpty, IsOptional, IsString, IsInt } from 'class-validator';

export class CreateBusinessRoleDto {
  @IsNotEmpty()
  @IsInt()
  business_department_id: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsNotEmpty()
  @IsInt()
  is_published: number;

  @IsOptional()
  @IsInt()
  created_by?: number;

  @IsOptional()
  @IsString()
  created_by_name?: string;

  @IsOptional()
  @IsInt()
  updated_by?: number;

  @IsOptional()
  @IsString()
  updated_by_name?: string;
}