 name: Dev Deployment

 on:
  push:
    branches: [ dev ]


 jobs:

  build:

    runs-on: ubuntu-latest

    steps:

      - name: Check Out Repo 
        uses: actions/checkout@v2

      - name: ENV Decode
        run: echo ${{ secrets.ENV_ENCODE_DEV }} | base64 -d > .env  

      - name: Login to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME_DEV }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD_DEV }}

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v1

      - name: get repository name
        run: echo "REPOSITORY_NAME=${GITHUB_REPOSITORY#*/}" >> $GITHUB_ENV

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          context: ./
          file: ./Dockerfile
          push: true
          tags: ${{ secrets.DOCKER_HUB_USERNAME_DEV }}/${{ env.REPOSITORY_NAME }}:${{ github.run_number }}

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

#      - name: Deploy updated code
#        uses: appleboy/ssh-action@v1.2.0
#        with:
#          host: ${{ secrets.HOST_DEV }}
#          username: ${{ secrets.SSH_USERNAME_DEV }}
#          key: ${{ secrets.KEY_DEV }}
#          script: cd /home/<USER>/${{ env.REPOSITORY_NAME }} && TAG=${{ github.run_number }} docker-compose up -d

      - name: Deploy updated code
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: ${{ secrets.HOST_DEV }}
          username: ${{ secrets.SSH_USERNAME_DEV }}
          key: ${{ secrets.KEY_DEV }}
          script: |
            cd /home/<USER>/${{ env.REPOSITORY_NAME }} && docker-compose down && TAG=${{ github.run_number }} docker-compose up -d

            
