{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,

    "esModuleInterop": true,
    "types": [
      // ... your other types
      "node"
    ],
  },
  "include": [
    "src/**/*.ts",
    "src/config/config.json" // Include config.json
  ]
}
