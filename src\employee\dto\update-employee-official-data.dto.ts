import { Transform, Type } from "class-transformer";
import {
  IsDate,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
} from "class-validator";

import { EMPLOYEE_TYPE } from "../../constants/api.enums";

export class updateEmployeeOfficialDataDto {
  @IsString()
  @IsNotEmpty()
  @Length(3, 255, { message: "Employee name must be string" })
  @Transform(({ value }) => value.trim())
  public readonly name: string;

  @IsString()
  @IsOptional()
  public readonly pro_pic: string;

  @IsEmail({}, { message: "Your email is invalid" })
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  public readonly email: string;

  @IsNumber(
    { allowInfinity: false, allowNaN: false },
    { message: "Department Id must be number" } // Custom error message
  )
  @IsNotEmpty()
  public readonly department_id: number;

  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  public readonly department: string;

  @IsNumber(
    { allowInfinity: false, allowNaN: false },
    { message: "Designation Id must be number" } // Custom error message
  )
  @IsNotEmpty()
  public readonly business_role_id: number;

  //@IsOptional()
  // @IsNotEmpty()
  // @IsDate()
  // public readonly join_date: Date;

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date) // Converts string to Date instance
  public readonly join_date: Date;

  @IsOptional()
  public readonly manager_id: number;

  @IsOptional()
  public readonly old_manager_id: number;

  @IsOptional()
  public readonly employee_id: string;

  @IsOptional()
  public readonly grade: string;

  @IsNotEmpty()
  public readonly employee_type: EMPLOYEE_TYPE;

  @IsNotEmpty()
  public readonly profile_id: number;

  @IsOptional()
  @IsString()
  public readonly rfid: string;
  
  @IsOptional()
  expense_unit: string;

  @IsOptional()
  work_station: string;
}