import { IsNotEmpty, IsOptional, IsString, IsInt, IsDate } from 'class-validator';

export class CreateJobPostDto {
  @IsNotEmpty()
  @IsInt()
  business_id: number;

  @IsNotEmpty()
  @IsString()
  job_title: string;

  @IsNotEmpty()
  @IsString()
  job_location: string;

  @IsNotEmpty()
  @IsInt()
  no_of_vacancies: number;

  @IsOptional()
  @IsString()
  experience_required?: string;

  @IsOptional()
  @IsString()
  job_salary?: string;

  @IsNotEmpty()
  @IsString()
  employment_status: string;

  @IsNotEmpty()
  @IsDate()
  deadline: Date;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  job_context?: string;

  @IsOptional()
  @IsString()
  job_responsibilities?: string;

  @IsOptional()
  @IsString()
  educational_requirements?: string;

  @IsOptional()
  @IsString()
  additional_requirements?: string;

  @IsOptional()
  @IsString()
  compensation_benefits?: string;
}