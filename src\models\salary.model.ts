import {
    Model,
    Column,
    DataType,
    Table,
    <PERSON><PERSON>ey,
    BelongsTo,
    HasMany,
} from "sequelize-typescript";
import { BusinessMembersModel } from "./BusinessMember.model";

@Table({
    tableName: "salaries",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
})
export class SalariesModel extends Model<SalariesModel> {
    @Column({
        type: DataType.BIGINT,
        autoIncrement: true,
        primaryKey: true,
    })
    id: number;

    @ForeignKey(() => BusinessMembersModel)
    @Column({
        type: DataType.BIGINT,
        allowNull: false,
    })
    business_member_id: number;

    @Column({
        type: DataType.DECIMAL(10, 2),
        allowNull: false,
    })
    gross_salary: number;

    @Column({
        type: DataType.BIGINT,
        allowNull: false,
    })
    created_by: number;

    @Column({
        type: DataType.STRING,
        allowNull: false,
    })
    created_by_name: string;

    @Column({
        type: DataType.BIGINT,
        allowNull: false,
    })
    updated_by: number;

    @Column({
        type: DataType.STRING,
        allowNull: false,
    })
    updated_by_name: string;

    @Column({
        type: DataType.DATE,
        allowNull: false,
    })
    created_at: Date;

    @Column({
        type: DataType.DATE,
        allowNull: false,
    })
    updated_at: Date;

    // Relation with BusinessMembersModel table
    @BelongsTo(() => BusinessMembersModel)
    businessMember: BusinessMembersModel; // Will add business_member_id in the SalariesModel table

}