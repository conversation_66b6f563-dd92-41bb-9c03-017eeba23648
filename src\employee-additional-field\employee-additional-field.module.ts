import { Module } from '@nestjs/common';
import { EmployeeAdditionalFieldService } from './employee-additional-field.service';
import { EmployeeAdditionalFieldController } from './employee-additional-field.controller';
import { DatabaseModule } from "../config/database/database.module";
import { AuthService } from "src/auth/auth.service";

@Module({
  imports: [DatabaseModule],
  controllers: [EmployeeAdditionalFieldController],
  providers: [EmployeeAdditionalFieldService, AuthService],
})
export class EmployeeAdditionalFieldModule {}
