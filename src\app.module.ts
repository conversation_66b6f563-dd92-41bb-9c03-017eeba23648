import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { MulterModule } from "@nestjs/platform-express";
import multer from "multer";
import { LoggerMiddleware } from "src/middleware/loggerMiddleware";
import { InviteEmployeeMail } from "../utils/mail/invite-employee";
import { AttendanceDeviceModule } from "./attendance-device/attendance-device.module";
import { AuthService } from "./auth/auth.service";
import { BusinessRoleController } from "./business-role/business-role.controller";
import { BusinessRoleModule } from "./business-role/business-role.module";
import { BusinessRoleService } from "./business-role/business-role.service";
import { DatabaseModule } from "./config/database/database.module";
import { DepartmentController } from "./department/department.controller";
import { DepartmentModule } from "./department/department.module";
import { DepartmentService } from "./department/department.service";
import { EmployeeAdditionalFieldController } from "./employee-additional-field/employee-additional-field.controller";
import { EmployeeAdditionalFieldModule } from "./employee-additional-field/employee-additional-field.module";
import { EmployeeAdditionalFieldService } from "./employee-additional-field/employee-additional-field.service";
import { EmployeeController } from "./employee/employee.controller";
import { EmployeeModule } from "./employee/employee.module";
import { EmployeeService } from "./employee/employee.service";
import { DigitalOceanService } from "./file-upload/digital-ocean.service";
import { FileUploadModule } from "./file-upload/file-upload.module";
import { FileUploadService } from "./file-upload/file-upload.service";
import { KpiModule } from "./kpi-setup/kpi-setup.module";
import { DemoController } from "./modules/demo/demo.controller";
import { DemoModule } from "./modules/demo/demo.module";
import { DemoService } from "./modules/demo/demo.service";
import { RepositoryModule } from "./repositories/repository.module";
// import { TeammateAppreciationModule } from "./teammate-appreciation/teammate-appreciation.module";
import { RecruitmentManagementModule } from "./recruitment_management/recruitment_management.module";

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: [`.env.${process.env.NODE_ENV}`],
      isGlobal: true,
    }),
    MulterModule.register({
      storage: multer.memoryStorage(), // Use memory storage
    }),
    DatabaseModule,
    DemoModule,
    RepositoryModule,
    DepartmentModule,
    // TeammateAppreciationModule,
    RecruitmentManagementModule,
    BusinessRoleModule,
    EmployeeAdditionalFieldModule,

    MulterModule.register({
      storage: multer.memoryStorage(),
    }),
    EmployeeModule,
    FileUploadModule,
    AttendanceDeviceModule,
    KpiModule,
  ],
  controllers: [
    DemoController,
    DepartmentController,
    BusinessRoleController,
    EmployeeAdditionalFieldController,
    EmployeeController,
  ],
  providers: [
    DemoService,
    DepartmentService,
    BusinessRoleService,
    EmployeeAdditionalFieldService,
    EmployeeService,
    AuthService,
    InviteEmployeeMail,
    FileUploadService,
    DigitalOceanService,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer): void {
    consumer.apply(LoggerMiddleware).forRoutes("*");
  }
}
