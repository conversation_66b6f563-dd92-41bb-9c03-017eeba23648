import {
	Table,
	Column,
	Model,
	DataType,
	ForeignKey,
	BelongsTo,
	HasMany,
	HasOne,
} from "sequelize-typescript";
import { ProfilesModel } from "./Profile.model";
import { BusinessMembersModel } from "./BusinessMember.model";

//@Table({ tableName: 'members', timestamps: true })
@Table({ tableName: "members" })
export class MembersModel extends Model {
	@Column({
		type: DataType.BIGINT,
		autoIncrement: true,
		primaryKey: true,
	})
	id: number;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	remember_token: string;

	@ForeignKey(() => ProfilesModel)
	@Column({
		type: DataType.INTEGER,
		allowNull: false,
	})
	profile_id: number; // Primary key of profiles table

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	father_name: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	spouse_name: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	mother_name: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	nid_no: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	nid_image: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	emergency_contract_person_name: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	alternate_contact: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	emergency_contract_person_number: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	emergency_contract_person_relationship: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	education: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	references: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	bank_account: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	mfs_account: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	other_expertise: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	experience: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	present_income: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	ward_no: string;

	@Column({
		type: DataType.TEXT,
		allowNull: true,
	})
	police_station: string;

	@Column({
		type: DataType.INTEGER,
		defaultValue: 0, // value is 0,1
		allowNull: false,
	})
	is_verified: number;

	@Column({
		type: DataType.JSONB,
		allowNull: true,
	})
	social_links: object;

	@Column({
		type: DataType.INTEGER,
		defaultValue: 0,
		allowNull: false,
	})
	created_by: number; // Profiles table primary key

	@Column({
		type: DataType.STRING,
		allowNull: false,
	})
	created_by_name: string; // Profiles table name

	@Column({
		type: DataType.INTEGER,
		defaultValue: 0,
		allowNull: false,
		//allowNull: true
	})
	updated_by: number; // Profiles table primary key

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	updated_by_name: string; // Profiles table name

	@Column({
		type: DataType.DATE,
		defaultValue: DataType.NOW,
		allowNull: false,
	})
	created_at: Date;

	@Column({
		type: DataType.DATE,
		//defaultValue: DataType.NOW,
		allowNull: true, // maybe allowNull: true
	})
	updated_at: Date;

	// @Column({
	//   type: DataType.DATE,
	//   allowNull: true,
	// })
	// deletedAt: Date;

	// Relationship
	@BelongsTo(() => ProfilesModel, { foreignKey: 'profile_id', as: 'profile' })
	profile: ProfilesModel;

	@HasOne(() => BusinessMembersModel)
	businessMembers: BusinessMembersModel;
}
