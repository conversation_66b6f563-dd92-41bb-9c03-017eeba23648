import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export function IsDifferentId(property: string, validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'isDifferentId',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [property],
            validator: {
                validate(value: any, args: ValidationArguments) {
                    const relatedValue = (args.object as any)[args.constraints[0]];
                    return value !== relatedValue; // Ensure the values are different
                },
                defaultMessage(args: ValidationArguments) {
                    return `${args.property} must not be the same as ${args.constraints[0]}`;
                },
            },
        });
    };
} 