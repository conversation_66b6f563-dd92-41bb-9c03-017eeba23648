import {
	Table,
	Column,
	Model,
	DataType,
	ForeignKey,
	BelongsTo,
	CreatedAt,
	UpdatedAt,
} from "sequelize-typescript";
import { KpiModel } from "./kpi.model";
import { BusinessDepartmentsModel } from "./BusinessDepartment.model";

@Table({
	tableName: "appraisal_departments",
	timestamps: true,
})
export class AppraisalDepartmentModel extends Model<AppraisalDepartmentModel> {
	@Column({
		type: DataType.BIGINT,
		primaryKey: true,
		autoIncrement: true,
	})
	id: number;

	@ForeignKey(() => KpiModel)
	@Column({
		type: DataType.INTEGER,
		allowNull: false,
	})
	kpiId: number;

	@ForeignKey(() => BusinessDepartmentsModel)
	@Column({
		type: DataType.INTEGER,
		allowNull: false,
	})
	departmentId: number;
	@Column({
		type: DataType.DATE,
		allowNull: true,
	})
	due_date: Date; // Added due_date column
	@BelongsTo(() => KpiModel)
	kpi: KpiModel;

	@BelongsTo(() => BusinessDepartmentsModel)
	department: BusinessDepartmentsModel;

	@CreatedAt
	@Column({ field: "created_at" })
	created_at: Date;

	@UpdatedAt
	@Column({ field: "updated_at" })
	updated_at: Date;
}
