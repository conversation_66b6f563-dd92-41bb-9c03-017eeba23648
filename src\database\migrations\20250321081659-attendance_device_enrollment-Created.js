"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("attendance_device_enrollment", {
      id: {
        type: Sequelize.DataTypes.BIGINT,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      device_identifier: {
        type: Sequelize.DataTypes.STRING,
        allowNull: false,
      },
      hand: {
        type: Sequelize.DataTypes.ENUM("left", "right"), // Restrict values
        allowNull: false,
      },
      finger: {
        type: Sequelize.DataTypes.ENUM(
          "thumb",
          "index",
          "middle",
          "ring",
          "pinky"
        ), // Restrict values
        allowNull: false,
      },
      business_member_id: {
        type: Sequelize.DataTypes.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: "business_member", // Ensure this matches the actual table name
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.DataTypes.NOW,
      },

      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.DataTypes.NOW,
        onUpdate: Sequelize.DataTypes.NOW,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("attendance_device_enrollment");
  },
};
