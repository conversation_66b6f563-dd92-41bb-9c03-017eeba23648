import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { Op } from "sequelize";
import { ErrorLog } from "src/config/winstonLog";
import { BusinessDepartmentsModel } from "../models/BusinessDepartment.model";
import { CreateDepartmentDto } from "./dto/create-department.dto";
import { UpdateDepartmentDto } from "./dto/update-departmetn.dto";
import { errorResponse, successResponse } from "utils/response";
import { BusinessMembersModel, BusinessRolesModel } from "src/models";

@Injectable()
export class DepartmentService {
    async create(createdepartmentDto: CreateDepartmentDto, loginUser: any) {
        try {
            const existingDepartment = await BusinessDepartmentsModel.findOne({
                where: {
                    business_id: loginUser.business_id,
                    name: createdepartmentDto.name
                }
            });
    
            if (existingDepartment) {
                throw new HttpException(
                    'Department with this name already exists',
                    HttpStatus.BAD_REQUEST
                );
            }
            const obj = {
                business_id: loginUser.business_id,
                is_published: 1,
                created_by: loginUser.business_member_id,
                updated_by: loginUser.business_member_id,
                created_by_name: `Member-${loginUser.name}`,
                updated_by_name: `Member-${loginUser.name}`,
                created_at: Date.now(),
                logo_original: "asdf",
            };
            const objData = { ...createdepartmentDto, ...obj };
            const department = await BusinessDepartmentsModel.create(
                objData as any
            );
            return successResponse(
                department,
                "Department created successfully",
                HttpStatus.CREATED
            );
        } catch (error) {
            ErrorLog("Department", "department-create", error);
            return errorResponse(
                error?.message || "Failed to create department data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async findOne(id: number): Promise<any> {
        try {
            const department = await BusinessDepartmentsModel.findByPk(id);
            if (!department) {
                throw new HttpException(
                    "Department not found",
                    HttpStatus.NOT_FOUND
                );
            }
            return successResponse(
                department,
                "Department get successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Department", "department-findOne", error);
            return errorResponse(
                error?.message || "Failed to fetch department data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async update(
        id: number,
        updateDepartmentDto: UpdateDepartmentDto,
        loginUser: any
    ): Promise<any> {
        try {
            if (updateDepartmentDto.name) {
                const existingDepartment = await BusinessDepartmentsModel.findOne({
                    where: {
                        business_id: loginUser.business_id,
                        name: updateDepartmentDto.name,
                        id: { [Op.ne]: id } // Exclude current department
                    }
                });
    
                if (existingDepartment) {
                    throw new HttpException(
                        'Department with this name already exists',
                        HttpStatus.BAD_REQUEST
                    );
                }
            }
            await BusinessDepartmentsModel.update(updateDepartmentDto, {
                where: { id },
            });
            const department = await BusinessDepartmentsModel.findByPk(id);
            return successResponse(
                department,
                "Department updated successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Department", "department-update", error);
            return errorResponse(
                error?.message || "Failed to update department data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async remove(id: number) {
        try {
            await BusinessDepartmentsModel.destroy({ where: { id } });
            return successResponse(
                {},
                "Department deleted successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Department", "department-delete", error);
            return errorResponse(
                error?.message || "Failed to delete department data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async findAll(skip: number, limit: number, is_all: boolean, search = "", loginUser: any, is_published?: number) {
        try {
            const where: any = {
                business_id: loginUser.business_id,
            };
            if (search) {
                where.name = {
                    [Op.like]: `%${search}%`,
                };
            }
            if (!isNaN(is_published)) {
                console.log("is_published", is_published);
                where.is_published = is_published;
            }

            const queryOptions: any = {
                where: where,
                include: [
                    {
                        model: BusinessRolesModel,
                        attributes: ['id'],
                        include: [
                            {
                                model: BusinessMembersModel,
                                attributes: ['id'],
                                required: false
                            }
                        ]
                    }
                ]
            };

            const totalCount = await BusinessDepartmentsModel.count({
                where: where,
                distinct: true
            });

            // Only add limit/offset if not requesting all
            if (!is_all) {
                queryOptions.limit = limit;
                queryOptions.offset = skip;
            }

            const departments = await BusinessDepartmentsModel.findAll(queryOptions);
            // Transform the response to include employee counts
            const transformedDepartments = departments.map(dept => {
                const employeeCount = dept.roles.reduce((acc, role) => {
                    return acc + (role.businessMembers?.length || 0);
                }, 0);

                return {
                    ...dept.toJSON(),
                    employee_count: employeeCount,
                    roles: undefined // Remove the roles data from response if not needed
                };
            });

            return successResponse(
                {
                    totalLength: totalCount,
                    currentLength: transformedDepartments.length,
                    departments: transformedDepartments,
                },
                "Department fetched successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Department", "department-list", error);
            return errorResponse(
                error?.message || "Failed to fetch department data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async departmentStatusUpdate(
        id: number,
        is_published: number,
        loginUser: any
    ) {
        try {
            const department = await BusinessDepartmentsModel.findByPk(id);
            if (!department) {
                throw new HttpException(
                    "Department not found",
                    HttpStatus.NOT_FOUND
                );
            }
            if (is_published == 0) {
                const employeeCount = await BusinessMembersModel.count({
                    include: [{
                        model: BusinessRolesModel,
                        required: true,
                        where: {
                            business_department_id: id
                        }
                    }]
                });
    
                if (employeeCount > 0) {
                    throw new HttpException(
                        "Cannot unpublish department with existing employees",
                        HttpStatus.BAD_REQUEST
                    );
                }
            }
            department.is_published = is_published;
            department.updated_by = loginUser?.business_member_id;
            department.updated_by_name = `Member-${loginUser.name}`;
            await department.save();
            return successResponse(
                {},
                "Department published successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Department status", "employee-status", error);
            return errorResponse(
                error?.message || "Failed to department published",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
