import { Injectable } from "@nestjs/common";
import * as XLSX from "xlsx";
@Injectable()
export class FileUploadService {
	parseExcel(buffer: Buffer): any[] {
		let workbook: XLSX.WorkBook | null = null;
		let jsonData: any[] | null = null;
		try {
			workbook = XLSX.read(buffer, { type: "buffer" });

			if (!workbook.SheetNames.length) {
				throw new Error("No sheets found in the Excel file");
			}

			const sheetName = workbook.SheetNames[0];
			const worksheet = workbook.Sheets[sheetName];

			if (!worksheet) {
				throw new Error(
					`Sheet ${sheetName} not found in the Excel file`,
				);
			}

			jsonData = XLSX.utils.sheet_to_json(worksheet);
			console.log("json data:", jsonData);

			return jsonData;
		} catch (error) {
			console.error("Error parsing Excel file:", error.message);
			throw new Error("Failed to parse Excel file");
		} finally {
			buffer = null;
			workbook = null;
			jsonData = null;
		}
	}
}
