import {
	Table,
	Column,
	Model,
	DataType,
	ForeignKey,
	BelongsTo,
	CreatedAt,
	UpdatedAt,
	Default,
} from "sequelize-typescript";
import { BusinessMembersModel } from "./BusinessMember.model";
import { BusinessDepartmentsModel } from "./BusinessDepartment.model";
import { KpiModel } from "./kpi.model";

@Table({
	tableName: "employee_kpis",
	timestamps: true,
})
export class EmployeeKpiModel extends Model<EmployeeKpiModel> {
	@Column({
		type: DataType.BIGINT,
		primaryKey: true,
		autoIncrement: true,
	})
	id: number;

	@ForeignKey(() => BusinessMembersModel)
	@Column({
		type: DataType.INTEGER.UNSIGNED,
		allowNull: false,
	})
	employeeId: number;

	@BelongsTo(() => BusinessMembersModel)
	employee: BusinessMembersModel;

	@Column({
		type: DataType.STRING,
		allowNull: false,
	})
	designation: string;

	@ForeignKey(() => BusinessDepartmentsModel)
	@Column({
		type: DataType.INTEGER.UNSIGNED,
		allowNull: false,
	})
	departmentId: number;

	@BelongsTo(() => BusinessDepartmentsModel)
	department: BusinessDepartmentsModel;

	@ForeignKey(() => KpiModel)
	@Column({
		type: DataType.INTEGER,
		allowNull: false,
	})
	kpiId: number;

	@BelongsTo(() => KpiModel)
	kpi: KpiModel;

	@Column({
		type: DataType.STRING,
		allowNull: false,
	})
	unit: string;

	@ForeignKey(() => BusinessMembersModel)
	@Column({
		type: DataType.INTEGER.UNSIGNED,
		allowNull: false,
	})
	lineManagerId: number;

	@BelongsTo(() => BusinessMembersModel, "lineManagerId")
	lineManager: BusinessMembersModel;

	@Column({
		type: DataType.STRING,
		allowNull: false,
	})
	lineManagerName: string;

	@Column({
		type: DataType.STRING,
		allowNull: false,
	})
	lineManagerDesignation: string;

	@Column({
		type: DataType.JSON,
		allowNull: true,
	})
	kpiArray: {
		kpiType: string;
		weightage: number;
		details: string;
		comment: string;
		ratingByOwn: number;
		ratingByLineManager: number;
		lineManagerEvaluation: string;
	}[];

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	lineManagerComment: string;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	attachment: string;

	@Column({
		type: DataType.BOOLEAN,
		allowNull: true,
	})
	recommendationForPromotion: boolean;

	@Column({
		type: DataType.STRING,
		allowNull: true,
	})
	adminComment: string;

	@Column({
		type: DataType.ENUM("kpi", "appraisal"),
		allowNull: false,
		defaultValue: "kpi",
	})
	positionStatus: "kpi" | "appraisal";

	@Column({
		type: DataType.ENUM("pending", "reject", "complete"),
		allowNull: true,
		defaultValue: "pending",
	})
	status: "pending" | "reject" | "complete";
	// @CreatedAt
	// @Column({ field: "created_at" })
	// created_at: Date;

	// @UpdatedAt
	// @Column({ field: "updated_at" })
	// updated_at: Date;
}
