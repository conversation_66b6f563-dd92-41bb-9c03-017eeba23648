import {
	IsString,
	IsEmail,
	<PERSON><PERSON>nt,
	<PERSON>,
	<PERSON>,
	Length,
	IsDate,
	IsArray,
	ArrayNotEmpty,
	ValidateNested,
	IsUrl,
	IsNotEmpty,
	IsNumber,
	IsMobilePhone,
	IsOptional,
	IsObject,
} from "class-validator";
import { Type } from "class-transformer";

import { GENDER, BLOOD_GROUP } from "../../constants/api.enums";
import { Json } from "sequelize/types/utils";

export class updateEmployeePersonalDataDto {
	@IsString()
	@IsNotEmpty()
	//@Length(3, 255, { message: "Employee name must be string" })
	mobile: string;

	@IsString()
	@IsNotEmpty()
	gender: GENDER;

	@IsOptional()
	@IsDate()
	@Type(() => Date) // Converts string to Date instance
	dob: Date;

	@IsString()
	@IsOptional()
	address: string;

	@IsString()
	@IsOptional()
	nationality: string;

	@IsString()
	@IsOptional()
	nid_no: string;

	@IsString()
	@IsOptional()
	nid_image_front: string;

	@IsString()
	@IsOptional()
	nid_image_back: string;

	@IsString()
	@IsOptional()
	passport_no: string;

	@IsString()
	@IsOptional()
	passport_image: string;

	@IsString()
	@IsOptional()
	blood_group: BLOOD_GROUP;

	// @IsString()
	// @IsOptional()
	// social_links: JSON;

	@IsObject()
	social_links: {
		linkedIn: string;
		facebook: string;
		instagram: string;
	};

	// @IsString()
	// @IsOptional()
	// fb_id: string;

	@IsNotEmpty()
	profile_id: number;

	@IsNotEmpty()
	@IsNumber()
	member_id: number;
}
