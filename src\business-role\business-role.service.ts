import { Injectable, HttpException, HttpStatus } from "@nestjs/common";
import { Op } from "sequelize";
import { ErrorLog } from "src/config/winstonLog";
import { BusinessRolesModel } from "../models/BusinessRole.model";
import { CreateBusinessRoleDto } from "./dto/create-business-role.dto";
import { UpdateBusinessRoleDto } from "./dto/update-business-role.dto";
import { BusinessDepartmentsModel } from "../models/BusinessDepartment.model";
import { errorResponse, successResponse } from "utils/response";
import { BusinessMembersModel } from "src/models";

@Injectable()
export class BusinessRoleService {
    async findOneNotByPK(obj: any): Promise<BusinessRolesModel> {
        return await BusinessRolesModel.findOne({ where: obj });
    }

    async create(
        createBusinessRoleDto: CreateBusinessRoleDto,
        loginUser: any
    ): Promise<any> {
        try {
            const existingRole = await BusinessRolesModel.findOne({
                where: {
                    business_department_id: createBusinessRoleDto.business_department_id,
                    name: createBusinessRoleDto.name
                }
            });
    
            if (existingRole) {
                throw new HttpException(
                    'Designation with this name already exists in the department',
                    HttpStatus.BAD_REQUEST
                );
            }
            const obj = {
                business_id: loginUser.business_id,
                is_published: 1,
                created_by: loginUser.business_member_id,
                updated_by: loginUser.business_member_id,
                created_by_name: `Member-${loginUser.name}`,
                updated_by_name: `Member-${loginUser.name}`,
                created_at: Date.now(),
            };
            const objData = { ...createBusinessRoleDto, ...obj };
            const role = await BusinessRolesModel.create(objData as any);
            return successResponse(
                { role: role },
                "Business role created successfully",
                HttpStatus.CREATED
            );
        } catch (error) {
            ErrorLog(error?.message, error);
            return errorResponse(
                error?.message || `Business role creation Error`,
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async findAll(
        skip: number,
        limit: number,
        is_all: boolean,
        search = "",
        is_published: number,
        business_department_id: number,
        loginUser: any
    ) {
        try {
            const where: any = {
                [Op.or]: [{ name: { [Op.substring]: search } }],
            };
            if (!isNaN(is_published)) {
                console.log("is_published", is_published);
                where.is_published = is_published;
            }
            if (business_department_id)
                where.business_department_id = business_department_id;

            const queryOptions: any = {
                where: where,
                include: [
                    {
                        model: BusinessDepartmentsModel,
                        attributes: ["id", "name", "business_id"],
                        where: {
                            business_id: loginUser?.business_id,
                        },
                    },
                    {
                        model: BusinessMembersModel,
                        attributes: ['id'],
                        required: false
                    }
                ],
            };

            const totalCount = await BusinessRolesModel.count({
                where: where,
                include: [{
                    model: BusinessDepartmentsModel,
                    where: {
                        business_id: loginUser?.business_id,
                    }
                }],
                distinct: true
            });
    

            // Only add limit/offset if not requesting all
            if (!is_all) {
                queryOptions.limit = limit;
                queryOptions.offset = skip;
            }

            const roles = await BusinessRolesModel.findAll(queryOptions);
        
            // Transform the response to include employee counts
            const transformedRoles = roles.map(role => {
                return {
                    ...role.toJSON(),
                    employee_count: role.businessMembers?.length || 0,
                    businessMembers: undefined // Remove the members data from response
                };
            });
            return successResponse(
                {
                    totalLength: totalCount,
                    currentLength: transformedRoles.length,
                    roles: transformedRoles,
                },
                "Business role fetched successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Business role", "business-role-list", error);
            return errorResponse(
                error?.message || `Failed to fetch business role data`,
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async findOne(id: number): Promise<any> {
        try {
            const role = await BusinessRolesModel.findByPk(id);
            if (!role)
                throw new HttpException("Id not found", HttpStatus.NOT_FOUND);

            return successResponse(
                { role: role },
                "Business role found successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Business role", "business-role-find", error);
            return errorResponse(
                error?.message || "Failed to fetch business role data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async update(
        id: number,
        updateBusinessRoleDto: UpdateBusinessRoleDto,
        loginUser: any
    ): Promise<any> {
        try {
            const role = await BusinessRolesModel.findByPk(id);
            if (!role)
                throw new HttpException("Id not found", HttpStatus.NOT_FOUND);

            if (updateBusinessRoleDto.name) {
                const existingRole = await BusinessRolesModel.findOne({
                    where: {
                        business_department_id: updateBusinessRoleDto.business_department_id || role.business_department_id,
                        name: updateBusinessRoleDto.name,
                        id: { [Op.ne]: id } // Exclude current role
                    }
                });
    
                if (existingRole) {
                    throw new HttpException(
                        'Designation with this name already exists in the department',
                        HttpStatus.BAD_REQUEST
                    );
                }
            }
            await role.update(updateBusinessRoleDto);
            return successResponse(
                { role: role },
                "Business role updated successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Business role", "business-role-update", error);
            return errorResponse(
                error?.message || "Failed to update business role data",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async businessRoleStatusUpdate(
        id: number,
        is_published: number,
        loginUser: any
    ) {
        try {
            const role = await BusinessRolesModel.findByPk(id);
            if (!role) {
                throw new HttpException(
                    "Business role not found",
                    HttpStatus.NOT_FOUND
                );
            }
            if (is_published == 0) {
                const employeeCount = await BusinessMembersModel.count({
                    include: [{
                        model: BusinessRolesModel,
                        required: true,
                        where: {
                            id: id
                        }
                    }]
                });
    
                if (employeeCount > 0) {
                    throw new HttpException(
                        "Cannot unpublish designation with existing employees",
                        HttpStatus.BAD_REQUEST
                    );
                }
            }
            role.is_published = is_published;
            role.updated_by = loginUser?.business_member_id;
            role.updated_by_name = `Member-${loginUser.name}`;
            await role.save();
            return successResponse(
                {},
                "Business role published successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Business status", "business-role-status", error);
            return errorResponse(
                error?.message || "Failed to business role published",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async remove(id: number) {
        try {
            await BusinessRolesModel.destroy({ where: { id } });
            return successResponse(
                {},
                "Business role deleted successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog("Business delete", "business-role-delete", error);
            return errorResponse(
                error?.message || "Failed to delete business role",
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
