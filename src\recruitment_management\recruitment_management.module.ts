import { Module } from '@nestjs/common';
import { RecruitmentManagementService } from "./recruitment_management.service";
import { RecruitmentManagementController } from "./recruitment_management.controller";
import { DatabaseModule } from "../config/database/database.module";
import { AuthService } from "src/auth/auth.service";

@Module({
    imports: [DatabaseModule],
    controllers: [RecruitmentManagementController],
    providers: [RecruitmentManagementService, AuthService],
})
export class RecruitmentManagementModule {}
