import { Table, Column, Model, DataType, HasMany  } from 'sequelize-typescript';
import { BusinessMembersModel  } from "./BusinessMember.model";
import { BusinessMemberAdditionalSectionsModel } from './BusinessMemberAdditionalSection.model';
import { COMPANY_TYPE,
  HALF_DAY_CONFIGURATION,
  SHIFT_CONFIGURATION,
  LIVE_TRACK_CONFIGURATION,
  EMPLOYEE_VISIT_CONFIGURATION,
  PAYROLL_CONFIGURATION,
  FISCAL_YEAR_CONFIGURATION,
  SANDWICH_LEAVE_CONFIGURATION,
  LEAVE_PRORATE_CONFIGURATION
} from '../constants/api.enums';

//@Table({ tableName: 'businesses', timestamps: true })
@Table({ tableName: 'businesses', charset: "utf8mb4",	collate: "utf8mb4_unicode_ci", })
export class BusinessesModel extends Model {
  @Column({
    type: DataType.BIGINT,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  sub_domain: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tagline: string;

  @Column({ 
    type: DataType.ENUM(...Object.values(COMPANY_TYPE)), // Dynamically load enum values
    allowNull: false,
    defaultValue: COMPANY_TYPE.COMPANY,
  })
  type: string;

  @Column({
    type: DataType.INTEGER, // Dynamically load enum values
    allowNull: false,
    defaultValue: HALF_DAY_CONFIGURATION.DISABLE,
  })
  is_half_day_enable: HALF_DAY_CONFIGURATION;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  half_day_configuration: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  business_category_id: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  shift_start_date: string;

  @Column({
    type: DataType.INTEGER, // Dynamically load enum values
    allowNull: false,
    defaultValue: SHIFT_CONFIGURATION.DISABLE,
  })
  is_shift_enable: SHIFT_CONFIGURATION;

  @Column({
    type: DataType.INTEGER, // Dynamically load enum values
    allowNull: false,
    defaultValue: LIVE_TRACK_CONFIGURATION.DISABLE,
  })
  is_live_track_enable: LIVE_TRACK_CONFIGURATION;

  @Column({
    type: DataType.INTEGER, // Dynamically load enum values
    allowNull: false,
    defaultValue: EMPLOYEE_VISIT_CONFIGURATION.DISABLE,
  })
  is_enable_employee_visit: number;

  @Column({
    type: DataType.INTEGER, // Dynamically load enum values
    allowNull: false,
    defaultValue: PAYROLL_CONFIGURATION.DISABLE,
  })
  is_payroll_enable: PAYROLL_CONFIGURATION;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  website: string;
  
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description: string;
  
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  about: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  address: string;
  
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  geo_informations: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  logo: string;
  
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  logo_original: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  logo_coordinates: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  employee_size: number;

  @Column({
    type: DataType.INTEGER, // Dynamically load enum values
    allowNull: false,
    defaultValue: FISCAL_YEAR_CONFIGURATION.July,
  })
  fiscal_year: FISCAL_YEAR_CONFIGURATION;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  registration_year: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  registration_no: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  establishment_year: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  trade_license: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tin_no: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  company_type: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  map_location: string;

  @Column({
    type: DataType.INTEGER, // Dynamically load enum values
    allowNull: false,
    defaultValue: SANDWICH_LEAVE_CONFIGURATION.DISABLE,
  })
  is_sandwich_leave_enable: SANDWICH_LEAVE_CONFIGURATION;

  @Column({
    type: DataType.INTEGER, // Dynamically load enum values
    allowNull: false,
    defaultValue: LEAVE_PRORATE_CONFIGURATION.DISABLE,
  })
  is_leave_prorate_enable: LEAVE_PRORATE_CONFIGURATION;

  @Column({
    type: DataType.DECIMAL(11,10),
    defaultValue: 0.00,
    allowNull: false,
  })
  xp: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  rating: number;

  @Column({
    type: DataType.DECIMAL(11,10),
    defaultValue: 0.00,
    allowNull: false,
  })
  wallet: number;

  @Column({
    type: DataType.DECIMAL(11,10),
    defaultValue: 1000.00,
    allowNull: false,
  })
  topup_prepaid_max_limit: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false,
  })
  account_completion: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false,
  })
  is_verified: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  user_id: number;
  
  @Column({
    type: DataType.INTEGER,
    allowNull: false
  })
  created_by: number; // Profiles table primary key
  
  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  created_by_name: string; // Profiles table name

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    //allowNull: false
    allowNull: true
  })
  updated_by: number; // Profiles table primary key
  
  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  updated_by_name: string; // Profiles table name

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    allowNull: false
    //allowNull: true
  })
  is_published: number;
  
  @Column({
    type: DataType.DATE,
    allowNull: false,
    //defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true, // maybe allowNull: true
    //defaultValue: DataType.NOW
  })
  updated_at: Date;

  // @Column({
  //   type: DataType.DATE,
  //   allowNull: true,
  // })
  // deletedAt: Date;

  // Relation
  @HasMany(() => BusinessMembersModel)
  businessMembers: BusinessMembersModel[];

  @HasMany(() => BusinessMemberAdditionalSectionsModel)
  additional_section: BusinessMemberAdditionalSectionsModel[];
}