import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { Response } from "express";
import { ServiceTokenGuard } from "src/guards";
import { DigitalOceanService } from "./../file-upload/digital-ocean.service";
import { ActivateInviteEmployeeDto } from "./dto/activate-invite-employee.dto";
import { CreateEmployeeDto } from "./dto/create-employee.dto";
import { EmployeeStatusBulkUpdateDto } from "./dto/employee-status-bulk-update.dto";
import { InviteEmployeeDto } from "./dto/invite-employee.dto";
import { ReinviteEmailDto } from "./dto/reinvite-email.dto";
import { updateEmployeeAdditionalDataDto } from "./dto/update-employee-additional-data.dto";
import { updateEmployeeEmergencyContactDataDto } from "./dto/update-employee-emergency-contact-data.dto";
import { updateEmployeeFinancialDataDto } from "./dto/update-employee-financial-data.dto";
import { updateEmployeeOfficialDataDto } from "./dto/update-employee-official-data.dto";
import { updateEmployeePersonalDataDto } from "./dto/update-employee-personal-data.dto";
import { UpdateMemberTypesIDDto } from "./dto/update-member-types-id.dto";
import { UpdateMemberTypesDto } from "./dto/update-member-types.dto";
import { UpdateProfilePictureDto } from "./dto/update-profile-picture.dto";
import { EmployeeService } from "./employee.service";

@Controller("employee-management")
export class EmployeeController {
  constructor(
    private readonly employeeService: EmployeeService,
    private readonly digitalOceanService: DigitalOceanService
  ) {}

  @UseGuards(ServiceTokenGuard)
  @Post("/v1/create")
  async create(
    @Req() req,
    @Body() createEmployeeDto: CreateEmployeeDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.create(
      createEmployeeDto,
      req?.user,
      req?.headers
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/activate-invite-employee/:businessMemberId")
  async activateInvitedEmployee(
    @Req() req,
    @Param("businessMemberId") businessMemberId: string,
    @Body() createEmployeeDto: ActivateInviteEmployeeDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.activateInvitedEmployee(
      createEmployeeDto,
      +businessMemberId,
      req?.user,
      req?.headers
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Post("/v1/invite-with-email")
  async inviteWithEmail(
    @Req() req,
    @Body() inviteEmployeeDto: InviteEmployeeDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.inviteWithEmail(
      inviteEmployeeDto,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/re-invite-with-email/:businessMemberId")
  async reInviteWithEmail(
    @Req() req,
    @Body() reInvtiteEmailDto: ReinviteEmailDto,
    @Param("businessMemberId") businessMemberId: string,
    @Res() res: Response
  ) {
    let data = await this.employeeService.reinviteEmail(
      reInvtiteEmailDto,
      +businessMemberId,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Get("v1/data")
  async findAll(
    @Req() req,
    @Query("skip") skip: string,
    @Query("limit") limit: string,
    @Query("business_department_id") business_department_id: number,
    @Query("business_department_ids") business_department_ids: string,
    @Query("business_role_id") business_role_id: number,
    @Query("status") status: string,
    @Query("employee_type") employee_type: string,
    @Query("search") search: string,
    @Query("search_type") search_type: string,
    @Query("manager_id") manager_id: number,
    @Query("is_wifi_network_active") is_wifi_network_active: number,
    @Query("is_location_active") is_location_active: number,
    @Query("is_remote_active") is_remote_active: number,
    @Query("is_in_attendance_device") is_in_attendance_device: boolean,
    @Res()
    res: Response
  ) {
    let is_all = false;
    if (
      (skip == "0" && limit == "0") ||
      skip == undefined ||
      limit == undefined
    ) {
      is_all = true;
    }
    const skipNumber = parseInt(skip, 10) || 0;
    const limitNumber = parseInt(limit, 10) || 10;
    const departmentIds = business_department_ids
      ? business_department_ids.split(",").map((id) => parseInt(id.trim(), 10))
      : business_department_id
        ? [business_department_id]
        : undefined;
    let data = await this.employeeService.findAll(
      skipNumber,
      limitNumber,
      business_department_id,
      business_role_id,
      status,
      employee_type,
      search,
      search_type,
      manager_id,
      req?.user,
      is_all,
      is_wifi_network_active,
      is_location_active,
      is_remote_active,
      departmentIds,
      is_in_attendance_device
    );
    return res.status(data.statusCode).json(data);
  }

  @Get("/v1/details/:id")
  @UseGuards(ServiceTokenGuard)
  async findOne(@Param("id") id: string, @Req() req, @Res() res: Response) {
    let data = await this.employeeService.findOne(+id, req?.user);
    return res.status(data.statusCode).json(data);
  }

  @Get("/v1/details-me")
	@UseGuards(ServiceTokenGuard)
	async findOneMe(@Req() req, @Res() res: Response) {

		let data = await this.employeeService.findOne(Number(req?.user?.business_member_id), req?.user);
		return res.status(data.statusCode).json(data);
	}

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/update-official-data/:id")
  async updateOfficialData(
    @Req() req,
    @Param("id") id: string,
    @Body() updateEmployeeOfficialDataDto: updateEmployeeOfficialDataDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.updateOfficialData(
      +id,
      updateEmployeeOfficialDataDto,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/update-personal-data/:id")
  async updatePersonalData(
    @Req() req,
    @Param("id") id: string,
    @Body() updateEmployeePersonalDataDto: updateEmployeePersonalDataDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.updatePersonalData(
      +id,
      updateEmployeePersonalDataDto,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/update-financial-data/:id")
  async updateFinancialData(
    @Req() req,
    @Param("id") id: string,
    @Body() updateEmployeeFinancialDataDto: updateEmployeeFinancialDataDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.updateFinancialData(
      +id,
      updateEmployeeFinancialDataDto,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/update-emergency-contact/:id")
  async updateEmergencyData(
    @Req() req,
    @Param("id") id: string,
    @Body()
    updateEmployeeEmergencyContactDataDto: updateEmployeeEmergencyContactDataDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.updateEmergencyData(
      +id,
      updateEmployeeEmergencyContactDataDto,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/update-additional-data/:id")
  async updateAdditionalData(
    @Req() req,
    @Param("id") id: string,
    @Body()
    updateEmployeeAdditionalDataDto: updateEmployeeAdditionalDataDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.updateAdditionalData(
      +id,
      updateEmployeeAdditionalDataDto,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/employee-status-update/:id")
  async employeeStatusUpdate(
    @Req() req,
    @Param("id") id: string,
    @Body("status") status: string,
    @Res() res: Response
  ) {
    let data = await this.employeeService.employeeStatusUpdate(
      +id,
      status,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/employee-status-bulk-update")
  async employeeStatusBulkUpdate(
    @Req() req,
    @Body() employeeStatusBulkUpdateDto: EmployeeStatusBulkUpdateDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.employeeStatusBulkUpdate(
      employeeStatusBulkUpdateDto,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.employeeService.remove(+id);
  }

  @Patch("v1/create-or-update-bkash-data-of-employee")
  @UseInterceptors(FileInterceptor("file"))
  @UseGuards(ServiceTokenGuard)
  createOrUpdateBkashData(
    @UploadedFile() file: Express.Multer.File,
    @Req() req
  ) {
    if (!file) {
      throw new Error("File is missing");
    }
    return this.employeeService.fileUploader(file.buffer, req?.user);
  }

  // @UseGuards(ServiceTokenGuard)
  @Patch("/v1/update-photo")
  @UseInterceptors(FileInterceptor("file"))
  async uploadImage(@UploadedFile() file: Express.Multer.File) {
    let uploadResult;
    console.log("file :", file);
    if (file) {
      const key = `upload/${Date.now()}-${file.originalname}`;
      uploadResult = await this.digitalOceanService.uploadFile(file, key);
      // updateEmployeePersonalDataDto.nid_image_front = uploadResult.Location; // Example of setting the uploaded file URL
    }

    return uploadResult;
  }

  @UseGuards(ServiceTokenGuard)
  @Patch("/v1/update-profile-picture/:id")
  async updateProfilePicture(
    @Req() req,
    @Param("id") id: string,
    @Body() updateProfilePictureDto: UpdateProfilePictureDto,
    @Res() res: Response
  ) {
    let data = await this.employeeService.updateProfilePicture(
      +id,
      updateProfilePictureDto.pro_pic,
      req?.user
    );
    return res.status(data.statusCode).json(data);
  }

  @UseGuards(ServiceTokenGuard)
  @Get("v1/business/:id")
  async getBusinessDetails(
    @Param("id") id: number,
    @Req() req,
    @Res() res: Response
  ) {
    let data = await this.employeeService.getBusinessDetails(id);
    return res.status(data.statusCode).json(data);
  }
  @UseGuards(ServiceTokenGuard)
  @Delete("/v1/invitation-delete/:id")
  async removeBusinessMember(
    @Param("id") businessMemberId: number,
    @Req() req,
    @Res() res: Response
  ): Promise<any> {
    const result = await this.employeeService.removeBusinessMember(
      businessMemberId,
      req?.user
    );

    return res.status(result.statusCode).json(result);
  }

  @UseGuards(ServiceTokenGuard)
  @Get("/v1/new-joiner-co-worker")
  async getNewJoinerCoWorker(
    @Res() res: Response,
    @Req() req,
    @Query("departmentId") businessDepartmentId: number
  ): Promise<any> {
    console.log("req user: ", req.user);
    const result = await this.employeeService.getRecentBusinessMembers(
      req?.user,
      businessDepartmentId
    );

    return res.status(result.statusCode).json(result);
  }

  @UseGuards(ServiceTokenGuard)
  @Get("/v1/dashboard-cart-analytics-data")
  async getYearBasedData(
    @Res() res: Response,
    @Req() req,
    @Query("year") year: number,
    @Query("businessId") businessId: number
  ): Promise<any> {
    console.log("hello jp", typeof year);
    const result = await this.employeeService.getYearBasedData(
      year,
      businessId
    );

    return res.status(result.statusCode).json(result);
  }

  @Get("v1/download/details/:id")
  @UseGuards(ServiceTokenGuard)
  async pdfGenerate(@Req() req, @Param("id") id: string, @Res() res: Response) {
    let pdfBuffer = await this.employeeService.pdfGenerate(+id, req?.user);
    res.set({
      "Content-Type": "application/pdf",
      "Content-Disposition": 'attachment; filename="co_worker_details.pdf"',
    });
    const startTime = new Date();
    const endTime = new Date();
    console.log(`🔹 PDF generation ended at: ${endTime.toISOString()}`);
    console.log(
      `🔹 PDF generation duration: ${(endTime.getTime() - startTime.getTime()) / 1000} seconds`
    );

    // res.status(200).send("HELLO");
    res.send(pdfBuffer);
  }

  @Get("v1/employees-list-download")
  @UseGuards(ServiceTokenGuard)
  async getEmployees(@Req() req, @Res() res: Response) {
    const data = await this.employeeService.getEmployeeList(req.user);
    return res.status(data.statusCode).json(data);
  }

  @Post("/update-member-types")
  async updateMemberTypes(@Body() updateMemberTypesDto: UpdateMemberTypesDto) {
    await this.employeeService.updateMemberTypes(updateMemberTypesDto);
    return { message: "Member types updated successfully" };
  }

  @Post("/update-member-types-with-id")
  async updateMemberTypesWithID(
    @Body() updateMemberTypesIDDto: UpdateMemberTypesIDDto
  ) {
    await this.employeeService.updateMemberTypesWithID(updateMemberTypesIDDto);
    return { message: "Member types updated successfully" };
  }

  @Post("/update-manager-types")
  async updateManagerTypes() {
    await this.employeeService.updateManagerTypes();
    return { message: "Member types updated successfully" };
  }

  @Post("/update-null-types")
  async updateNullTypesToEmployee() {
    return this.employeeService.updateNullTypesToEmployee();
  }
}
