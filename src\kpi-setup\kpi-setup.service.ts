import { HttpStatus, Injectable } from "@nestjs/common";
import { errorResponse, successResponse } from "utils/response";
import { InfoLog, ErrorLog } from "../config/winstonLog";
import { Op } from "sequelize";
import {
	BusinessDepartmentsModel,
	KpiModel,
	KpiDepartmentModel,
	EmployeeKpiModel,
	AppraisalDepartmentModel,
	BusinessMembersModel,
	ProfilesModel,
	MembersModel,
} from "src/models";
import { CreateKpiDto } from "./dto/create-kpi.dto";
import { OpenAppraisalDto } from "./dto/open-appraisal.dto";

@Injectable()
export class KpiService {
	constructor() {}

	// Create KPI
	async createKpi(data: CreateKpiDto) {
		try {
			InfoLog("Creating KPI", "KPI CREATE");

			// Ensure department IDs are unique
			const departmentIds = data.departments.map((d) => d.departmentId);
			const uniqueDepartmentIds = new Set(departmentIds);

			if (departmentIds.length !== uniqueDepartmentIds.size) {
				ErrorLog(
					"Department IDs must be unique",
					"KPI CREATE",
					new Error("Duplicate department IDs found"),
				);
				return errorResponse(
					"Department IDs must be unique.",
					null,
					HttpStatus.BAD_REQUEST,
				);
			}
			const { departments, ...kpiData } = data;
			// Create KPI
			const kpi = await KpiModel.create(kpiData);

			// Create department associations
			const departmentData = data.departments.map((d) => ({
				kpiId: kpi.id,
				departmentId: d.departmentId,
				due_date: d.due_date,
			}));
			await KpiDepartmentModel.bulkCreate(departmentData);

			InfoLog("KPI created successfully", "KPI CREATE");
			return successResponse(
				kpi,
				"KPI created successfully",
				HttpStatus.CREATED,
			);
		} catch (error) {
			ErrorLog("Error creating KPI", "KPI CREATE", error);
			return errorResponse(
				error?.message || "Error creating KPI",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	// Get All KPIs
	async getAllKpis({
		skip,
		limit,
		search,
		departmentId,
	}: {
		skip?: number;
		limit?: number;
		search?: string;
		departmentId?: number;
	}) {
		console.log("jp");
		console.log("skip limit", skip, limit);
		try {
			InfoLog("Fetching all KPIs", "KPI FETCH");

			const whereClause: any = {};
			if (search) {
				whereClause.kpi_title = {
					[Op.like]: `%${search}%`,
				};
			}

			// Add department filter if provided
			const include: any[] = [
				{
					model: KpiDepartmentModel,
					as: "departments",
					include: [
						{
							model: BusinessDepartmentsModel,
							attributes: ["id", "name"],
						},
					],
				},
				{
					model: AppraisalDepartmentModel,
					as: "appraisalDepartments",
					include: [
						{
							model: BusinessDepartmentsModel,
							attributes: ["id", "name"],
						},
					],
				},
			];
			if (departmentId) {
				include[0].where = { departmentId };
			}
			console.log("whereClause", include);
			const { rows: kpis, count: total } = await KpiModel.findAndCountAll(
				{
					where: whereClause,
					include,
					offset: skip,
					limit,
				},
			);

			InfoLog(
				`KPIs fetched successfully. Total: ${total}, Current: ${kpis.length}`,
				"KPI FETCH",
			);
			return successResponse(
				{ kpis, totalLength: total, currentLength: kpis.length },
				"KPIs fetched successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog("Error fetching KPIs", "KPI FETCH", error);
			return errorResponse(
				error?.message || "Error fetching KPIs",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	// Get KPI by ID
	async getKpiById(id: number) {
		InfoLog("Fetching KPI by ID", "KPI FETCH");
		try {
			const kpi = await KpiModel.findByPk(id, {
				include: [
					{
						model: KpiDepartmentModel,
						as: "departments",
						include: [
							{
								model: BusinessDepartmentsModel,
								attributes: ["id", "name"],
							},
						],
					},
					{
						model: AppraisalDepartmentModel,
						as: "appraisalDepartments",
						include: [
							{
								model: BusinessDepartmentsModel,
								attributes: ["id", "name"],
							},
						],
					},
				],
			});

			if (!kpi) {
				ErrorLog(
					"KPI not found",
					"KPI FETCH",
					new Error(`KPI ID: ${id} not found`),
				);
				return errorResponse(
					"KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}

			InfoLog("KPI fetched successfully", "KPI FETCH");
			return successResponse(
				kpi,
				"KPI fetched successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog("Error fetching KPI", "KPI FETCH", error);
			return errorResponse(
				error?.message || "Error fetching KPI",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	// Open Appraisal
	async openAppraisal(id: number, data: OpenAppraisalDto) {
		try {
			InfoLog("Opening appraisal for KPI", "KPI APPRAISAL");

			// Fetch the KPI with its departments
			const kpi = await KpiModel.findByPk(id, {
				include: [
					{
						model: KpiDepartmentModel,
						as: "departments",
					},
				],
			});

			if (!kpi) {
				ErrorLog(
					"KPI not found",
					"KPI APPRAISAL",
					new Error(`KPI ID: ${id} not found`),
				);
				return errorResponse(
					"KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}
			console.log("kpi", kpi);
			// Update the due_date for the specified departments
			const updatedDepartments = kpi.departments
				.map((department) => {
					const updatedDepartment = data.departments.find(
						(d) => d.departmentId === department.departmentId,
					);
					if (updatedDepartment) {
						return {
							kpiId: id, // Ensure kpiId is included
							departmentId: department.departmentId,
							due_date: updatedDepartment.due_date, // Update the due_date
						};
					}
					return null;
				})
				.filter((d) => d !== null); // Filter out null values

			// Save the updated departments back to the KPI
			await KpiDepartmentModel.bulkCreate(updatedDepartments, {
				updateOnDuplicate: ["due_date"],
			});

			InfoLog("Appraisal opened successfully", "KPI APPRAISAL");
			return successResponse(
				kpi,
				"Appraisal opened successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog("Error opening appraisal", "KPI APPRAISAL", error);
			return errorResponse(
				error?.message || "Error opening appraisal",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	// Update KPI
	async updateKpi(
		id: number,
		data: {
			plan_type?: string;
			plan_period?: string;
			cycle_time_start?: Date;
			cycle_time_end?: Date;
			departments?: { departmentId: number; due_date: Date }[];
		},
	) {
		InfoLog(`Updating KPI with ID: ${id}`, "KPI UPDATE");
		try {
			const kpi = await KpiModel.findByPk(id);

			if (!kpi) {
				ErrorLog(`KPI with ID: ${id} not found`, "KPI UPDATE");
				return errorResponse(
					"KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}

			// Update fields
			if (data.plan_type) kpi.plan_type = data.plan_type;
			if (data.plan_period) kpi.plan_period = data.plan_period;
			if (data.cycle_time_start)
				kpi.cycle_time_start = data.cycle_time_start;
			if (data.cycle_time_end) kpi.cycle_time_end = data.cycle_time_end;

			// Update department-wise due dates
			if (data.departments) {
				const updatedDepartments = data.departments.map((d) => ({
					kpiId: kpi.id,
					departmentId: d.departmentId,
					due_date: d.due_date,
				}));
				await KpiDepartmentModel.bulkCreate(updatedDepartments, {
					updateOnDuplicate: ["due_date"],
				});
			}

			await kpi.save();

			InfoLog(`KPI with ID: ${id} updated successfully`, "KPI UPDATE");
			return successResponse(
				kpi,
				"KPI updated successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog(`Error updating KPI with ID: ${id}`, "KPI UPDATE", error);
			return errorResponse(
				error?.message || "Error updating KPI",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async createEmployeeKpi(data: any) {
		try {
			console.log("hello api");
			InfoLog("Creating Employee KPI", "EMPLOYEE_KPI_CREATE");

			// Create Employee KPI
			const employeeKpi = await EmployeeKpiModel.create(data);

			InfoLog("Employee KPI created successfully", "EMPLOYEE_KPI_CREATE");
			return successResponse(
				employeeKpi,
				"Employee KPI created successfully",
				HttpStatus.CREATED,
			);
		} catch (error) {
			ErrorLog(
				"Error creating Employee KPI",
				"EMPLOYEE_KPI_CREATE",
				error,
			);
			return errorResponse(
				error?.message || "Error creating Employee KPI",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async getOwnSubmittedKpis({
		employeeId,
		lineManagerId,
		skip,
		limit,
		status,
		recommendationForPromotion,
		positionStatus,
		title,
		startDate,
		endDate,
	}: {
		employeeId?: number; // Made optional
		lineManagerId?: number;
		skip?: number;
		limit?: number;
		status?: string;
		recommendationForPromotion?: string;
		positionStatus?: string;
		title?: string;
		startDate?: Date;
		endDate?: Date;
	}) {
		try {
			InfoLog("Fetching own submitted KPIs", "OWN_KPI_FETCH");

			// Build where clause
			const whereClause: any = {};

			if (employeeId) {
				whereClause.employeeId = employeeId; // Filter by employeeId if provided
			}
			if (lineManagerId) {
				whereClause.lineManagerId = lineManagerId; // Filter by lineManagerId if provided
			}

			if (status) {
				whereClause.status = status; // Filter by status
			}

			if (recommendationForPromotion !== undefined) {
				// Convert string to boolean
				whereClause.recommendationForPromotion =
					recommendationForPromotion === "true";
			}

			if (positionStatus) {
				whereClause.positionStatus = positionStatus; // Filter by status
			}

			if (title) {
				whereClause.kpi_title = {
					[Op.like]: `%${title}%`, // Filter by title (partial match)
				};
			}

			if (startDate && endDate) {
				whereClause.createdat = {
					[Op.between]: [startDate, endDate], // Filter by date range
				};
			}

			// Fetch KPIs with pagination
			const { rows: kpis, count: total } =
				await EmployeeKpiModel.findAndCountAll({
					where: whereClause,
					include: [
						{
							model: BusinessMembersModel,
							as: "employee", // Ensure this matches the alias in the association
							attributes: ["id"], // Specify the fields you want to include
							include: [
								{
									model: MembersModel, // Include ProfileModel for deeper population
									as: "member", // Ensure this matches the alias in the association
									attributes: ["id"], // Specify the fields you want to include from ProfileModel
									include: [
										{
											model: ProfilesModel, // Include ProfileModel for deeper population
											as: "profile", // Ensure this matches the alias in the association
											attributes: ["id", "name", "email"], // Specify the fields you want to include from ProfileModel
										},
									],
								},
							],
						},
						{
							model: KpiModel,
							as: "kpi", // Ensure this matches the alias in the association
							attributes: ["id", "kpi_title", "plan_type"], // Specify the fields you want to include
						},
					],
					offset: skip,
					limit,
					order: [["createdat", "DESC"]], // Order by creation date (latest first)
				});

			InfoLog(
				`Own submitted KPIs fetched successfully. Total: ${total}, Current: ${kpis.length}`,
				"OWN_KPI_FETCH",
			);

			return successResponse(
				{ kpis, totalLength: total, currentLength: kpis.length },
				"Own submitted KPIs fetched successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog(
				"Error fetching own submitted KPIs",
				"OWN_KPI_FETCH",
				error,
			);
			return errorResponse(
				error?.message || "Error fetching own submitted KPIs",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async getEmployeeKpiById(id: number, positionStatus?: string) {
		try {
			InfoLog(
				`Fetching Employee KPI with ID: ${id}${positionStatus ? ` and positionStatus: ${positionStatus}` : ""}`,
				"EMPLOYEE_KPI_FETCH",
			);

			// Fetch Employee KPI by primary key
			const employeeKpi = await EmployeeKpiModel.findOne({
				where: {
					id,
					...(positionStatus && { positionStatus }), // Add positionStatus condition if provided
				},
				include: [
					{
						model: BusinessDepartmentsModel,
						attributes: ["id", "name"],
					},
					{
						model: KpiModel,
						attributes: [
							"id",
							"kpi_title",
							"plan_type",
							"plan_period",
							"cycle_time_start",
							"cycle_time_end",
						],
					},
				],
			});

			if (!employeeKpi) {
				ErrorLog(
					`Employee KPI with ID: ${id}${positionStatus ? ` and positionStatus: ${positionStatus}` : ""} not found`,
					"EMPLOYEE_KPI_FETCH",
					new Error(`Employee KPI ID: ${id} not found`),
				);
				return errorResponse(
					"Employee KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}

			InfoLog(
				`Employee KPI with ID: ${id}${positionStatus ? ` and positionStatus: ${positionStatus}` : ""} fetched successfully`,
				"EMPLOYEE_KPI_FETCH",
			);
			return successResponse(
				employeeKpi,
				"Employee KPI fetched successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog(
				`Error fetching Employee KPI with ID: ${id}${positionStatus ? ` and positionStatus: ${positionStatus}` : ""}`,
				"EMPLOYEE_KPI_FETCH",
				error,
			);
			return errorResponse(
				error?.message || "Error fetching Employee KPI",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async updateLineManagerCommentAndStatus(
		id: number,
		data: {
			lineManagerComment: string;
			status: "pending" | "reject" | "complete";
		},
	) {
		try {
			InfoLog(
				`Updating Employee KPI with ID: ${id}`,
				"EMPLOYEE_KPI_UPDATE",
			);

			// Find Employee KPI by primary key
			const employeeKpi = await EmployeeKpiModel.findByPk(id);

			if (!employeeKpi) {
				ErrorLog(
					`Employee KPI with ID: ${id} not found`,
					"EMPLOYEE_KPI_UPDATE",
					new Error(`Employee KPI ID: ${id} not found`),
				);
				return errorResponse(
					"Employee KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}

			// Update fields
			employeeKpi.lineManagerComment = data.lineManagerComment;
			employeeKpi.status = data.status;

			// Save the updated record
			await employeeKpi.save();

			InfoLog(
				`Employee KPI with ID: ${id} updated successfully`,
				"EMPLOYEE_KPI_UPDATE",
			);
			return successResponse(
				employeeKpi,
				"Employee KPI updated successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog(
				`Error updating Employee KPI with ID: ${id}`,
				"EMPLOYEE_KPI_UPDATE",
				error,
			);
			return errorResponse(
				error?.message || "Error updating Employee KPI",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async createAppraisalData(
		kpiId: number,
		departments: { departmentId: number; due_date: Date }[],
	) {
		try {
			InfoLog(
				`Creating appraisal data for KPI ID: ${kpiId}`,
				"APPRAISAL_CREATE",
			);

			// Check if the KPI exists
			const kpi = await KpiModel.findByPk(kpiId);
			if (!kpi) {
				ErrorLog(
					`KPI with ID: ${kpiId} not found`,
					"APPRAISAL_CREATE",
					new Error(`KPI ID: ${kpiId} not found`),
				);
				return errorResponse(
					"KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}

			// Ensure department IDs are unique
			const departmentIds = departments.map((d) => d.departmentId);
			const uniqueDepartmentIds = new Set(departmentIds);
			console.log("department id ", uniqueDepartmentIds);
			if (departmentIds.length !== uniqueDepartmentIds.size) {
				ErrorLog(
					"Department IDs must be unique",
					"APPRAISAL_CREATE",
					new Error("Duplicate department IDs found"),
				);
				return errorResponse(
					"Department IDs must be unique.",
					null,
					HttpStatus.BAD_REQUEST,
				);
			}

			// Create department associations
			const departmentData = departments.map((d) => ({
				kpiId,
				departmentId: d.departmentId,
				due_date: d.due_date,
			}));
			let data =
				await AppraisalDepartmentModel.bulkCreate(departmentData);
			// Bulk update EmployeeKpiModel
			const [updatedCount] = await EmployeeKpiModel.update(
				{
					positionStatus: "appraisal",
					status: null,
				},
				{
					where: {
						kpiId,
						departmentId: {
							[Op.in]: departmentIds, // Match department IDs
						},
					},
				},
			);
			InfoLog(
				`Appraisal data created successfully for KPI ID: ${kpiId}`,
				"APPRAISAL_CREATE",
			);
			return successResponse(
				{ kpiId, departments: departmentData },
				"Appraisal data created successfully",
				HttpStatus.CREATED,
			);
		} catch (error) {
			ErrorLog(
				`Error creating appraisal data for KPI ID: ${kpiId}`,
				"APPRAISAL_CREATE",
				error,
			);
			return errorResponse(
				error?.message || "Error creating appraisal data",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}
	async updateEmployeeApprisalArray(
		id: number,
		updates: { index: number; comment: string; ratingByOwn: number }[],
	) {
		try {
			InfoLog(
				`Updating KPI array for Employee KPI ID: ${id}`,
				"EMPLOYEE_KPI_ARRAY_UPDATE",
			);

			// Find Employee KPI by primary key
			const employeeKpi = await EmployeeKpiModel.findByPk(id);

			if (!employeeKpi) {
				ErrorLog(
					`Employee KPI with ID: ${id} not found`,
					"EMPLOYEE_KPI_ARRAY_UPDATE",
					new Error(`Employee KPI ID: ${id} not found`),
				);
				return errorResponse(
					"Employee KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}

			// Parse the existing kpiArray
			const kpiArray = employeeKpi.kpiArray || [];

			// Update the kpiArray based on the provided updates
			updates.forEach((update) => {
				if (kpiArray[update.index]) {
					kpiArray[update.index] = {
						...kpiArray[update.index],
						comment: update.comment,
						ratingByOwn: update.ratingByOwn,
					};
				}
			});

			// Assign the updated kpiArray and explicitly mark as changed
			employeeKpi.kpiArray = [...kpiArray];
			employeeKpi.changed("kpiArray", true);

			// Save only the updated field
			employeeKpi.status = "pending";
			await employeeKpi.save({ fields: ["kpiArray", "status"] });

			InfoLog(
				`KPI array for Employee KPI ID: ${id} updated successfully`,
				"EMPLOYEE_KPI_ARRAY_UPDATE",
			);

			return successResponse(
				employeeKpi,
				"KPI array updated successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog(
				`Error updating KPI array for Employee KPI ID: ${id}`,
				"EMPLOYEE_KPI_ARRAY_UPDATE",
				error,
			);
			return errorResponse(
				error?.message || "Error updating KPI array",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async updateLineManagerKpiArray(
		id: number,
		updates: {
			index: number;
			ratingByLineManager: number;
			lineManagerEvaluation: string;
		}[],
	) {
		try {
			InfoLog(
				`Updating KPI array for line manager in Employee KPI ID: ${id}`,
				"LINE_MANAGER_KPI_ARRAY_UPDATE",
			);

			// Find Employee KPI by primary key
			const employeeKpi = await EmployeeKpiModel.findByPk(id);

			if (!employeeKpi) {
				ErrorLog(
					`Employee KPI with ID: ${id} not found`,
					"LINE_MANAGER_KPI_ARRAY_UPDATE",
					new Error(`Employee KPI ID: ${id} not found`),
				);
				return errorResponse(
					"Employee KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}

			// Parse the existing kpiArray
			const kpiArray = employeeKpi.kpiArray || [];

			// Update the kpiArray based on the provided updates
			updates.forEach((update) => {
				if (kpiArray[update.index]) {
					kpiArray[update.index] = {
						...kpiArray[update.index],
						ratingByLineManager: update.ratingByLineManager,
						lineManagerEvaluation: update.lineManagerEvaluation,
					};
				}
			});

			// Reassign and mark as changed
			employeeKpi.kpiArray = [...kpiArray];
			employeeKpi.changed("kpiArray", true);

			// Also update other fields
			employeeKpi.positionStatus = "appraisal";
			employeeKpi.status = "complete";

			// Save the updated fields
			const data = await employeeKpi.save({
				fields: ["kpiArray", "positionStatus", "status"],
			});

			console.log("Updated employeeKpi.kpiArray:", data.kpiArray);

			InfoLog(
				`KPI array for line manager in Employee KPI ID: ${id} updated successfully`,
				"LINE_MANAGER_KPI_ARRAY_UPDATE",
			);

			return successResponse(
				employeeKpi,
				"KPI array updated successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog(
				`Error updating KPI array for line manager in Employee KPI ID: ${id}`,
				"LINE_MANAGER_KPI_ARRAY_UPDATE",
				error,
			);
			return errorResponse(
				error?.message || "Error updating KPI array",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async updateRecommendationAndAdminComment(
		id: number,
		data: { recommendationForPromotion: boolean; adminComment: string },
	) {
		try {
			InfoLog(
				`Updating recommendation and admin comment for Employee KPI ID: ${id}`,
				"EMPLOYEE_KPI_UPDATE",
			);

			// Find Employee KPI by primary key
			const employeeKpi = await EmployeeKpiModel.findByPk(id);

			if (!employeeKpi) {
				ErrorLog(
					`Employee KPI with ID: ${id} not found`,
					"EMPLOYEE_KPI_UPDATE",
					new Error(`Employee KPI ID: ${id} not found`),
				);
				return errorResponse(
					"Employee KPI not found",
					null,
					HttpStatus.NOT_FOUND,
				);
			}

			// Update fields
			employeeKpi.recommendationForPromotion =
				data.recommendationForPromotion;
			employeeKpi.adminComment = data.adminComment;

			// Save the updated record
			await employeeKpi.save();

			InfoLog(
				`Recommendation and admin comment for Employee KPI ID: ${id} updated successfully`,
				"EMPLOYEE_KPI_UPDATE",
			);
			return successResponse(
				employeeKpi,
				"Recommendation and admin comment updated successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog(
				`Error updating recommendation and admin comment for Employee KPI ID: ${id}`,
				"EMPLOYEE_KPI_UPDATE",
				error,
			);
			return errorResponse(
				error?.message ||
					"Error updating recommendation and admin comment",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async getDepartmentWiseKpis({
		departmentId,
		employeeId,
		startDate,
		endDate,
		skip,
		limit,
		positionStatus,
	}: {
		departmentId: number;
		employeeId: number;
		startDate?: Date;
		endDate?: Date;
		skip?: number;
		limit?: number;
		positionStatus: string;
	}) {
		try {
			InfoLog(
				`Fetching department-wise KPIs for department ID: ${departmentId} with positionStatus: ${positionStatus}`,
				"DEPARTMENT_WISE_KPI_FETCH",
			);

			// Build where clause
			const whereClause: any = {};

			if (startDate && endDate) {
				whereClause.created_at = {
					[Op.between]: [startDate, endDate], // Filter by date range
				};
			}
			console.log("whre clasue : ", whereClause);

			// Fetch KPIs with pagination and include EmployeeKpiModel if it exists
			const { rows: kpis, count: total } = await KpiModel.findAndCountAll(
				{
					where: whereClause,
					include: [
						{
							model: KpiDepartmentModel,
							as: "departments", // Ensure this matches the alias in the association
							attributes: ["id", "departmentId", "due_date"],
							where: { departmentId }, // Filter by departmentId
						},
						{
							model: EmployeeKpiModel,
							as: "employeeKpis",
							required: positionStatus === "appraisal", // Only include if positionStatus is "appraisal"
							where:
								positionStatus === "appraisal"
									? {
											...(employeeId && { employeeId }),
											positionStatus: "appraisal",
										}
									: {
											...(employeeId && { employeeId }),
											positionStatus: {
												[Op.ne]: "appraisal",
											}, // Exclude appraisal data
										},
						},
						{
							model: AppraisalDepartmentModel,
							as: "appraisalDepartments", // Ensure this matches the alias in the association
							attributes: ["id", "departmentId", "due_date"],
							where: { departmentId }, // Filter by departmentId
							required: false,
						},
					],
					offset: skip,
					limit,
					order: [["created_at", "DESC"]], // Order by creation date
				},
			);

			InfoLog(
				`Department-wise KPIs fetched successfully. Total: ${total}, Current: ${kpis.length}`,
				"DEPARTMENT_WISE_KPI_FETCH",
			);

			return successResponse(
				{ kpis, totalLength: total, currentLength: kpis.length },
				"Department-wise KPIs fetched successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog(
				`Error fetching department-wise KPIs for department ID: ${departmentId}`,
				"DEPARTMENT_WISE_KPI_FETCH",
				error,
			);
			return errorResponse(
				error?.message || "Error fetching department-wise KPIs",
				error?.response?.data || null,
				error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}
}
