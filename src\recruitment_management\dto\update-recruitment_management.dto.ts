import { IsOptional, IsString, IsInt, IsDate } from 'class-validator';

export class UpdateJobPostDto {
  @IsOptional()
  @IsInt()
  business_id?: number;

  @IsOptional()
  @IsString()
  job_title?: string;

  @IsOptional()
  @IsString()
  job_location?: string;

  @IsOptional()
  @IsInt()
  no_of_vacancies?: number;

  @IsOptional()
  @IsString()
  experience_required?: string;

  @IsOptional()
  @IsString()
  job_salary?: string;

  @IsOptional()
  @IsString()
  employment_status?: string;

  @IsOptional()
  @IsDate()
  deadline?: Date;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  job_context?: string;

  @IsOptional()
  @IsString()
  job_responsibilities?: string;

  @IsOptional()
  @IsString()
  educational_requirements?: string;

  @IsOptional()
  @IsString()
  additional_requirements?: string;

  @IsOptional()
  @IsString()
  compensation_benefits?: string;
}