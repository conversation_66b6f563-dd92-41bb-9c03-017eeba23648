import {
    Controller,
    Post,
    Get,
    Delete,
    Body,
    Param,
    Query,
    Patch,
    Req,
    UseGuards,
    Res,
} from "@nestjs/common";
import { DepartmentService } from "./department.service";
import { CreateDepartmentDto } from "./dto/create-department.dto";
import { UpdateDepartmentDto } from "./dto/update-departmetn.dto";
import { BusinessDepartmentsModel } from "../models/BusinessDepartment.model";
import { ServiceTokenGuard } from "src/guards";
import { Response } from "express";

@Controller("departments")
export class DepartmentController {
    constructor(private readonly departmentService: DepartmentService) {}

    @UseGuards(ServiceTokenGuard)
    @Post("v1/create")
    async createDepartment(
        @Req() req,
        @Body() createDepartmentDto: CreateDepartmentDto,
        @Res() res: Response
    ) {
        let data = await this.departmentService.create(
            createDepartmentDto,
            req?.user
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Get("v1/details/:id")
    async getDepartment(
        @Req() req,
        @Param("id") id: number,
        @Res() res: Response
    ) {
        let data = await this.departmentService.findOne(id);
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Patch("v1/update/:id")
    async updateDepartment(
        @Req() req,
        @Param("id") id: number,
        @Body() updateDepartmentDto: UpdateDepartmentDto,
        @Res() res: Response
    ) {
        let data = await this.departmentService.update(
            id,
            updateDepartmentDto,
            req?.user
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Patch("v1/department-status-update/:id")
    async departmentStatusUpdate(
        @Req() req,
        @Param("id") id: string,
        @Body("is_published") is_published: number,
        @Res() res: Response
    ) {
        let data = await this.departmentService.departmentStatusUpdate(
            +id,
            is_published,
            req?.user
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Delete(":id")
    async deleteDepartment(@Param("id") id: number, @Res() res: Response) {
        let data = await this.departmentService.remove(id);
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Get("v1/data")
    async findAll(
        @Req() req,
        @Query("skip") skip: string,
        @Query("limit") limit: string,
        @Query("search") search: string,
        @Query("is_published") is_published: string,
        @Res() res: Response
    ) {
        let is_all = false;
        if ((skip == "0" && limit == "0" ) || skip  == undefined || limit == undefined) {
            is_all = true;
        }
        const skipNumber = parseInt(skip, 10) || 0;
        const limitNumber = parseInt(limit, 10) || 10;
        const isPublishedNumber = is_published !== undefined ? parseInt(is_published, 10) : undefined;
        let data = await this.departmentService.findAll(
            skipNumber,
            limitNumber,
            is_all,
            search,
            req?.user,
            isPublishedNumber,
        );
        return res.status(data.statusCode).json(data);
    }
}
