import { Injectable, HttpException, HttpStatus } from "@nestjs/common";
import { Op } from "sequelize";
import { ErrorLog } from "src/config/winstonLog";
import { BusinessRolesModel } from "../models/BusinessRole.model";
import { CreateTeammateAppreciationDto } from "./dto/create-teammate-appreciation.dto";
import { BusinessDepartmentsModel } from "../models/BusinessDepartment.model";
import { errorResponse, successResponse } from "utils/response";
import {
    AppreciationModel,
    ProfilesModel,
    MembersModel,
    BusinessMembersModel,
    StickerModel,
    StickerCategoryModel,
} from "src/models";
import { UpdateTeammateAppreciationDto } from "./dto/update-teammate-appreciation.dto";
import { NOTIFICATION_API_URL } from "src/config/constants";
import axios from "axios";

@Injectable()
export class TeammateAppreciationService {
    async getSticker(loginUser: any, stickerCategoryId?: string) {
        try {
            let where = {};

            if (stickerCategoryId && stickerCategoryId.trim() !== "") {
                where = { sticker_category_id: stickerCategoryId };
            }

            const stickers = await StickerModel.findAndCountAll({
                where: where,
                order: [["created_at", "DESC"]],
                include: [
                    {
                        model: StickerCategoryModel,
                        attributes: ['name'],
                    }
                ],
            });
            const transformedStickers = stickers.rows.map(sticker => {
                const stickerData = sticker.toJSON();
                const { stickerCategory, ...rest } = stickerData;
                return {
                    ...rest,
                    sticker_category_name: stickerCategory?.name || null,
                };
            });

            return successResponse(
                { stickers: transformedStickers, total: stickers.count },
                "Stickers fetched successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog(error?.message, error);
            return errorResponse(
                error?.message || `Error fetching stickers`,
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async updateAppreciationNote(
        id: number,
        updateTeammateAppreciationDto: UpdateTeammateAppreciationDto,
        loginUser: any
    ) {
        try {
            const appreciation = await AppreciationModel.findByPk(id);

            if (!appreciation) {
                throw new HttpException(
                    'Appreciation not found',
                    HttpStatus.NOT_FOUND
                );
            }

            await appreciation.update({
                note: updateTeammateAppreciationDto.note,
                updated_by: loginUser.business_member_id,
                updated_by_name: loginUser.name,
            });

            return successResponse(
                { appreciation },
                'Appreciation note updated successfully',
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog(error?.message, error);
            return errorResponse(
                error?.message || `Error updating appreciation note`,
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }


    async createAppreciation(
        createTeammateAppreciationDto: CreateTeammateAppreciationDto,
        loginUser: any,
        headers: any,
    ) {
        try {
            const stickerExists = await StickerModel.findByPk(
                createTeammateAppreciationDto.sticker_id
            );

            if (!stickerExists) {
                throw new HttpException(
                    "Sticker ID does not exist",
                    HttpStatus.BAD_REQUEST
                );
            }

            const appreciation = await AppreciationModel.create({
                receiver_id: createTeammateAppreciationDto.receiver_id,
                giver_id: createTeammateAppreciationDto.giver_id,
                sticker_id: createTeammateAppreciationDto.sticker_id,
                created_by: loginUser.business_member_id,
                created_by_name: loginUser.name,
                updated_by: loginUser.business_member_id,
                updated_by_name: loginUser.name,
            });

            // notification .. send to reciever
            const giver = await BusinessMembersModel.findOne({
                where: { id: createTeammateAppreciationDto.giver_id },
                include: [{
                    model: MembersModel,
                    include: [{
                        model: ProfilesModel,
                        attributes: ['name']
                    }]
                }]
            });

            const giverName = giver?.member?.profile?.name || loginUser.name;

            try {
                const notificationPayload = {
                    receiver_business_member_id: createTeammateAppreciationDto.receiver_id,
                    type: 'appreciation-create',
                    title: `${giverName} appreciated you!`,
                    body: `${giverName} sent you an appreciation sticker`
                };
        
                await axios.post(
                    `${NOTIFICATION_API_URL}/notification-api/notification/v1/create-notification`,
                    notificationPayload,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: headers?.authorization,
                            productid: headers?.productid,
                        },
                    }
                );
            } catch (error) {
                ErrorLog('Failed to send notification about visit to assignee id', error);
            }

            return successResponse(
                { appreciation },
                "Appreciation given successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog(error?.message, error);
            return errorResponse(
                error?.message || `Error`,
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async getEmployeeAppreciationList(
        business_department_id: number,
        status = "",
        loginUser: any
    ) {
        try {
            const whereCondition: any = {
                business_id: loginUser.business_id,
                id: {
                    [Op.ne]: loginUser.business_member_id // Exclude the logged-in user
                }
            };

            if (status) {
				whereCondition.status = status;
			}
            // Get all employees from the same business
            const employees = await BusinessMembersModel.findAll({
                where: whereCondition,
                attributes: ["id"], // Only select necessary fields
                include: [
                    {
                        model: MembersModel,
                        attributes: ["id"],
                        include: [
                            {
                                model: ProfilesModel,
                                attributes: ["name", "pro_pic"],
                            },
                        ],
                    },
                    {
                        model: BusinessRolesModel,
                        attributes: ["name"],
                        where: business_department_id
                            ? { business_department_id }
                            : {},
                        include: [{
                            model: BusinessDepartmentsModel,
                            attributes: ["name"],
                        }]
                    },
                ],
            });

            // Get appreciations separately for better performance
            const appreciations = await AppreciationModel.findAll({
                where: {
                    giver_id: loginUser.business_member_id,
                },
                include: [
                    {
                        model: StickerModel,
                        attributes: ["id", "image"],
                    },
                ],
            });

            // Create a map of appreciations by receiver_id for faster lookup
            const appreciationsByReceiver = appreciations.reduce(
                (acc, appreciation) => {
                    if (!acc[appreciation.receiver_id]) {
                        acc[appreciation.receiver_id] = [];
                    }
                    acc[appreciation.receiver_id].push({
                        id: appreciation.id,
                        sticker: appreciation.sticker?.image,
                        note: appreciation.note,
                        created_at: appreciation.created_at,
                    });
                    return acc;
                },
                {}
            );

            // Transform the data to match required format
            const formattedEmployees = employees.map((employee) => ({
                id: employee.id,
                name: employee.member?.profile?.name,
                pro_pic: employee.member?.profile?.pro_pic,
                role: employee.role?.name,
                department_name: employee.role?.department?.name,
                appreciations: appreciationsByReceiver[employee.id] || [],
            }));

            return successResponse(
                { employees: formattedEmployees },
                "Employee appreciation list retrieved successfully",
                HttpStatus.OK
            );
        } catch (error) {
            ErrorLog(error?.message, error);
            return errorResponse(
                error?.message || `Failed to fetch employee appreciation list`,
                error?.response?.data || {},
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
