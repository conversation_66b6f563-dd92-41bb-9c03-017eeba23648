import { Table, Column, Model, DataType, Foreign<PERSON>ey, Has<PERSON>any } from 'sequelize-typescript';
import { BusinessesModel } from './Business.model';
import { JobApplicationModel } from './JobApplication.model';

@Table({ tableName: 'job_posts', timestamps: true })
export class JobPostModel extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => BusinessesModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  business_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: 'Application deadline',
  })
  deadline: Date;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'active',
    comment: 'Job post status: active, closed, etc.',
  })
  status: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  job_title: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  job_location: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  no_of_vacancies: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  experience_required: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  job_salary: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'full_time, part_time, contract',
  })
  employment_status: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  job_context: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  job_responsibilities: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  educational_requirements: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  additional_requirements: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  compensation_benefits: string;

  @HasMany(() => JobApplicationModel)
  applications: JobApplicationModel[];
}