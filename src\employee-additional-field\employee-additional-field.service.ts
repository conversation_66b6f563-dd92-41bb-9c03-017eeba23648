import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { Op } from "sequelize";
import { ErrorLog } from "src/config/winstonLog";
import { CreateEmployeeAdditionalFieldDto } from "./dto/create-employee-additional-field.dto";
import { UpdateEmployeeAdditionalFieldDto } from "./dto/update-employee-additional-field.dto";
import { BusinessMemberAdditionalFieldsModel } from "../models/BusinessMemberAdditionalField.model";
import { BusinessMemberAdditionalSectionsModel } from "../models/BusinessMemberAdditionalSection.model";
import { errorResponse, successResponse } from "utils/response";
import { BusinessMemberAdditionalDataModel } from "src/models";

@Injectable()
export class EmployeeAdditionalFieldService {
	async create(
		createEmployeeAdditionalFieldDto: CreateEmployeeAdditionalFieldDto,
		loginUser: any,
	) {
		try {
			const existingField =
				await BusinessMemberAdditionalFieldsModel.findOne({
					where: {
						section_id: createEmployeeAdditionalFieldDto.section_id,
						name: createEmployeeAdditionalFieldDto.name,
					},
				});

			if (existingField) {
				throw new HttpException(
					"An additional field with the same section and name already exists",
					HttpStatus.CONFLICT,
				);
			}

			const obj = {
				section_id: createEmployeeAdditionalFieldDto.section_id,
				name: createEmployeeAdditionalFieldDto.name,
				label: createEmployeeAdditionalFieldDto.label,
				type: createEmployeeAdditionalFieldDto.type,
				rules: createEmployeeAdditionalFieldDto.rules,
				created_by: loginUser.business_member_id,
				updated_by: loginUser.business_member_id,
				created_by_name: loginUser.name,
				updated_by_name: loginUser.name,
			};
			const additionalField =
				await BusinessMemberAdditionalFieldsModel.create(obj as any);

			return successResponse(
				additionalField,
				"Employee additional data field created successfully",
				HttpStatus.CREATED,
			);
		} catch (error) {
			ErrorLog("Additional Field", "additional-field-create", error);
			return errorResponse(
				error?.message || "Failed to create additional data field",
				error?.response?.data || {},
				error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async findAll(
		skip: number,
		limit: number,
		is_all: boolean,
		section_id = "",
		loginUser: any,
	) {
		try {
			let whereClause: any = {};
			if (section_id) {
				whereClause = {
					section_id: section_id,
				};
			}

			const options: any = {
				where: whereClause,
				include: [
					{
						model: BusinessMemberAdditionalSectionsModel,
						attributes: ["id", "name"],
						where: { business_id: loginUser.business_id },
					},
				],
			};

			// Only add limit/offset if not requesting all
			if (!is_all) {
				options.limit = limit;
				options.offset = skip;
			}

			const additionalFields =
				await BusinessMemberAdditionalFieldsModel.findAndCountAll(
					options,
				);

			return successResponse(
				{
					totalLength: additionalFields.count,
					currentLength: additionalFields.rows.length,
					additionalFields: additionalFields.rows,
				},
				"Employee additional data field fetched successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog("Additional Field", "additional-field-list", error);
			return errorResponse(
				error?.message || "Failed to find additional data fields",
				error?.response?.data || {},
				error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	findOne(id: number) {
		return `This action returns a #${id} employeeAdditionalField`;
	}

	async update(
		id: number,
		updateEmployeeAdditionalFieldDto: UpdateEmployeeAdditionalFieldDto,
		loginUser: any,
	) {
		try {
			const existingField =
				await BusinessMemberAdditionalFieldsModel.findOne({
					where: {
						section_id: updateEmployeeAdditionalFieldDto.section_id,
						name: updateEmployeeAdditionalFieldDto.name,
						id: { [Op.ne]: id }, // Exclude the current record
					},
				});

			if (existingField) {
				throw new HttpException(
					"An additional field with the same section and name already exists",
					HttpStatus.CONFLICT,
				);
			}

			const additionalField =
				await BusinessMemberAdditionalFieldsModel.update(
					{
						section_id: updateEmployeeAdditionalFieldDto.section_id,
						name: updateEmployeeAdditionalFieldDto.name,
						label: updateEmployeeAdditionalFieldDto.label,
						type: updateEmployeeAdditionalFieldDto.type,
						rules: updateEmployeeAdditionalFieldDto.rules,
						updated_by: loginUser.business_member_id,
						updated_by_name: loginUser.name,
					},
					{
						where: { id },
					},
				);

			return successResponse(
				await BusinessMemberAdditionalFieldsModel.findOne({
					where: { id },
				}),
				"Employee additional data field updated successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog("Additional Field", "additional-field-update", error);
			return errorResponse(
				error?.message || "Failed to update additional data field",
				error?.response?.data || {},
				error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async getAdditionalSection(loginUser: any) {
		try {
			const whereClause = {
				business_id: loginUser.business_id,
			};
			const additionalSections =
				await BusinessMemberAdditionalSectionsModel.findAll({
					where: whereClause,
				});
			const totalLength =
				await BusinessMemberAdditionalSectionsModel.count({
					where: whereClause,
				});

			return successResponse(
				{
					totalLength: totalLength,
					currentLength: additionalSections.length,
					additionalSections: additionalSections,
				},
				" Additional section fetched successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog("Additional Section", "additional-section-list", error);
			return errorResponse(
				error?.message || "Failed to find additional sections",
				error?.response?.data || {},
				error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async remove(id: number) {
		try {
			await BusinessMemberAdditionalFieldsModel.destroy({
				where: { id },
			});

			return successResponse(
				{},
				" Additional section deleted successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog("Additional Section", "additional-section-remove", error);
			return errorResponse(
				error?.message || "Failed to remove additional section",
				error?.response?.data || {},
				error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}

	async removeAdditionalFieldBySection(id: number, businessMemberId: number) {
		try {
			await BusinessMemberAdditionalDataModel.destroy({
				where: {
					business_member_id: businessMemberId,
					field_id: id,
				},
			});

			await BusinessMemberAdditionalFieldsModel.destroy({
				where: {
					id,
				},
			});
			return successResponse(
				{},
				" Additional field deleted successfully",
				HttpStatus.OK,
			);
		} catch (error) {
			ErrorLog("Additional Field", "additional-field-remove", error);
			return errorResponse(
				error?.message || "Failed to remove additional field",
				error?.response?.data || {},
				error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
			);
		}
	}
}
