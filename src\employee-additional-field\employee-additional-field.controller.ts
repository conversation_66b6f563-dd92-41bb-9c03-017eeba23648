import {
	Controller,
	Get,
	Post,
	Body,
	Patch,
	Param,
	Delete,
	Req,
	UseGuards,
	Query,
	Res,
} from "@nestjs/common";
import { EmployeeAdditionalFieldService } from "./employee-additional-field.service";
import { CreateEmployeeAdditionalFieldDto } from "./dto/create-employee-additional-field.dto";
import { UpdateEmployeeAdditionalFieldDto } from "./dto/update-employee-additional-field.dto";
import { BusinessMemberAdditionalFieldsModel } from "../models/BusinessMemberAdditionalField.model";
import { BusinessMemberAdditionalSectionsModel } from "../models/BusinessMemberAdditionalSection.model";
import { ServiceTokenGuard } from "src/guards";
import { Response } from "express";

@Controller("employee-additional-field")
export class EmployeeAdditionalFieldController {
	constructor(
		private readonly employeeAdditionalFieldService: EmployeeAdditionalFieldService,
	) {}

	@UseGuards(ServiceTokenGuard)
	@Post("v1/create")
	async create(
		@Req() req,
		@Body()
		createEmployeeAdditionalFieldDto: CreateEmployeeAdditionalFieldDto,
		@Res() res: Response,
	) {
		let data = await this.employeeAdditionalFieldService.create(
			createEmployeeAdditionalFieldDto,
			req?.user,
		);
		return res.status(data.statusCode).json(data);
	}

	@UseGuards(ServiceTokenGuard)
	@Get("v1/data")
	async findAll(
		@Req() req,
		@Query("skip") skip: string,
		@Query("limit") limit: string,
		@Query("section_id") section_id: string,
		@Res() res: Response,
	) {
		let is_all = false;
		if (
			(skip == "0" && limit == "0") ||
			skip == undefined ||
			limit == undefined
		) {
			is_all = true;
		}
		const skipNumber = parseInt(skip, 10) || 0;
		const limitNumber = parseInt(limit, 10) || 10;
		let data = await this.employeeAdditionalFieldService.findAll(
			skipNumber,
			limitNumber,
			is_all,
			section_id,
			req?.user,
		);
		return res.status(data.statusCode).json(data);
	}

	@UseGuards(ServiceTokenGuard)
	@Get("v1/details/:id")
	async findOne(@Param("id") id: string) {
		return this.employeeAdditionalFieldService.findOne(+id);
	}

	@UseGuards(ServiceTokenGuard)
	@Patch("v1/update/:id")
	async update(
		@Req() req,
		@Param("id") id: string,
		@Body()
		UpdateEmployeeAdditionalFieldDto: UpdateEmployeeAdditionalFieldDto,
		@Res() res: Response,
	) {
		let data = await this.employeeAdditionalFieldService.update(
			+id,
			UpdateEmployeeAdditionalFieldDto,
			req?.user,
		);
		return res.status(data.statusCode).json(data);
	}

	@UseGuards(ServiceTokenGuard)
	@Get("v1/additional-section")
	async getAdditionalSection(@Req() req, @Res() res: Response) {
		let data =
			await this.employeeAdditionalFieldService.getAdditionalSection(
				req?.user,
			);
		return res.status(data.statusCode).json(data);
	}

	@UseGuards(ServiceTokenGuard)
	@Delete(":id")
	async remove(@Param("id") id: string, @Res() res: Response) {
		let data = await this.employeeAdditionalFieldService.remove(+id);
		return res.status(data.statusCode).json(data);
	}

	@UseGuards(ServiceTokenGuard)
	@Delete("/remove-additional-data/:id/:businessMemberId")
	async removeBusinessMemberData(
		@Param("id") id: number,
		@Param("businessMemberId") businessMemberId: number,
		@Res() res: Response,
	) {
		let data =
			await this.employeeAdditionalFieldService.removeAdditionalFieldBySection(
				+id,
				+businessMemberId,
			);
		return res.status(data.statusCode).json(data);
	}
}
