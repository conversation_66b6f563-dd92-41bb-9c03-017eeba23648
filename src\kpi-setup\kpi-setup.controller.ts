import {
	Body,
	Controller,
	Get,
	Param,
	Patch,
	Post,
	Query,
	Res,
	HttpStatus,
} from "@nestjs/common";
import { Response } from "express";
import { CreateKpiDto } from "./dto/create-kpi.dto";
import { OpenAppraisalDto } from "./dto/open-appraisal.dto";
import { KpiService } from "./kpi-setup.service";

@Controller("kpi")
export class KpiController {
	constructor(private readonly kpiService: KpiService) {}

	@Post("/v1/create")
	async createKpi(@Body() dtoData: CreateKpiDto, @Res() res: Response) {
		const data = await this.kpiService.createKpi(dtoData);
		return res.status(data.statusCode).json(data);
	}

	@Get("/v1/get-all")
	async getAllKpis(
		@Query("skip") skip: number,
		@Query("limit") limit: number,
		@Query("search") search: string,
		@Query("departmentId") departmentId: string,
		@Res() res: Response,
	) {
		if (!skip) skip = 0;
		if (!limit) limit = 10;
		const data = await this.kpiService.getAllKpis({
			skip,
			limit,
			search: search,
			departmentId: departmentId ? parseInt(departmentId) : undefined,
		});
		return res.status(data.statusCode).json(data);
	}

	@Get("/v1/get-by-id/:kpiId")
	async getKpiById(@Param("kpiId") kpiId: number, @Res() res: Response) {
		const data = await this.kpiService.getKpiById(kpiId);
		return res.status(data.statusCode).json(data);
	}

	@Post("/v1/:kpiId")
	async openAppraisal(
		@Param("kpiId") kpiId: number,
		@Body() dtoData: OpenAppraisalDto,
		@Res() res: Response,
	) {
		const data = await this.kpiService.openAppraisal(kpiId, dtoData);
		return res.status(data.statusCode).json(data);
	}

	@Patch("/v1/update/:id")
	async updateKpi(
		@Param("id") id: number,
		@Body()
		data: {
			plan_type?: string;
			plan_period?: string;
			cycle_time_start?: Date;
			cycle_time_end?: Date;
			departments?: { departmentId: number; due_date: Date }[];
		},
		@Res() res: Response,
	) {
		const result = await this.kpiService.updateKpi(id, data);
		return res.status(result.statusCode).json(result);
	}
	@Patch("/v1/sumit-employee-kpi-data")
	async createEmployeeKpi(@Body() body: any, @Res() res: Response) {
		console.log("API /v1/sumit-employee-kpi-data called");
		const result = await this.kpiService.createEmployeeKpi(body);
		return res.status(result.statusCode).json(result);
	}

	@Get("own-submitted")
	async getOwnSubmittedKpis(
		@Res() res: Response,
		@Query("employeeId") employeeId?: number,
		@Query("lineManagerId") lineManagerId?: number,
		@Query("skip") skip?: number,
		@Query("limit") limit?: number,
		@Query("status") status?: string,
		@Query("positionStatus") positionStatus?: string,
		@Query("recommendationForPromotion")
		recommendationForPromotion?: string,
		@Query("title") title?: string,
		@Query("startDate") startDate?: string,
		@Query("endDate") endDate?: string,
	) {
		const result = await this.kpiService.getOwnSubmittedKpis({
			employeeId,
			lineManagerId,
			skip: skip ?? 0,
			limit: limit ?? 10,
			status,
			recommendationForPromotion,
			positionStatus,
			title,
			startDate: startDate ? new Date(startDate) : undefined,
			endDate: endDate ? new Date(endDate) : undefined,
		});
		return res.status(result.statusCode).json(result);
	}

	@Get("employee-kpi/:id")
	async getEmployeeKpiById(
		@Param("id") id: number,
		@Res() res: Response,
		@Query("positionStatus") positionStatus?: string,
	) {
		const result = await this.kpiService.getEmployeeKpiById(
			id,
			positionStatus,
		);
		return res.status(result.statusCode).json(result);
	}

	@Patch("/v1/employee-kpi/update-comment/:id")
	async updateLineManagerCommentAndStatus(
		@Param("id") id: number,
		@Body()
		body: {
			lineManagerComment: string;
			status: "pending" | "reject" | "complete";
		},
		@Res() res: Response,
	) {
		const result = await this.kpiService.updateLineManagerCommentAndStatus(
			id,
			body,
		);
		return res.status(result.statusCode).json(result);
	}

	@Post("/v1/appraisal/:kpiId")
	async createAppraisalData(
		@Param("kpiId") kpiId: number,
		@Body()
		body: { departments: { departmentId: number; due_date: Date }[] },
		@Res() res: Response,
	) {
		const result = await this.kpiService.createAppraisalData(
			kpiId,
			body.departments,
		);
		return res.status(result.statusCode).json(result);
	}

	@Patch("/v1/apprisal/submit/:id")
	async updateEmployeeKpiArray(
		@Param("id") id: number,
		@Body()
		body: {
			updates: { index: number; comment: string; ratingByOwn: number }[];
		},
		@Res() res: Response,
	) {
		const result = await this.kpiService.updateEmployeeApprisalArray(
			id,
			body.updates,
		);
		return res.status(result.statusCode).json(result);
	}

	@Patch("/v1/line-manager/apprisal/sbumit/:id")
	async updateLineManagerKpiArray(
		@Param("id") id: number,
		@Body()
		body: {
			updates: {
				index: number;
				ratingByLineManager: number;
				lineManagerEvaluation: string;
			}[];
		},
		@Res() res: Response,
	) {
		const result = await this.kpiService.updateLineManagerKpiArray(
			id,
			body.updates,
		);
		return res.status(result.statusCode).json(result);
	}

	@Patch("/v1/admin/update-recommendation/:id")
	async updateRecommendationAndAdminComment(
		@Param("id") id: number,
		@Body()
		body: { recommendationForPromotion: boolean; adminComment: string },
		@Res() res: Response,
	) {
		const result =
			await this.kpiService.updateRecommendationAndAdminComment(id, body);
		return res.status(result.statusCode).json(result);
	}

	@Get("/v1/get-department-wise-kpis")
	async getDepartmentWiseKpis(
		@Res() res: Response,
		@Query("departmentId") departmentId: number,
		@Query("employeeId") employeeId: number,
		@Query("positionStatus") positionStatus: string,
		@Query("startDate") startDate?: string,
		@Query("endDate") endDate?: string,
		@Query("skip") skip?: number,
		@Query("limit") limit?: number,
	) {
		const result = await this.kpiService.getDepartmentWiseKpis({
			departmentId,
			employeeId,
			startDate: startDate ? new Date(startDate) : undefined,
			endDate: endDate ? new Date(endDate) : undefined,
			skip: skip ?? 0,
			limit: limit ?? 10,
			positionStatus,
		});
		return res.status(result.statusCode).json(result);
	}
}
