import "dotenv/config";
import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { NestExpressApplication } from "@nestjs/platform-express";
import { expressBind } from "i18n-2";
import { localize } from "./middleware";
import { ValidateInputPipe } from "./middleware/validate";
import { nestwinstonLog, HttpPortLog } from "./config/winstonLog";
import fs from "fs";
const port = process.env.PORT || 3000;
import * as crypto from "crypto";
(global as any).crypto = crypto;

async function bootstrap() {
	//read ssl config....
	const httpsOptions = {
		key: fs.readFileSync("./ssl/keyfile-encrypted.key"),
		cert: fs.readFileSync("./ssl/97580e4c070d1482.crt"),
		ca: [fs.readFileSync("./ssl/gd1.crt")],
		passphrase: "<PERSON><PERSON><PERSON>@123##",
	};

	const NestFactoryOptions = { logger: nestwinstonLog };

	if (process.env.SSL === "true") {
		//enable ssl..
		NestFactoryOptions["httpsOptions"] = httpsOptions;
	}

	const app = await NestFactory.create<NestExpressApplication>(
		AppModule,
		NestFactoryOptions,
	);
	// global prefix
	app.setGlobalPrefix("/empl-mgnt-api");

	expressBind(app, { locales: ["en", "bn"] });

	app.use(localize);

	//handle browser cros..
	app.enableCors({
		origin: true, // or specify your frontend URL
		credentials: true,
		allowedHeaders: '*', // allow all headers
		exposedHeaders: ['Productid', 'Origin'], // if you want to read them in browser JS
	});

	// handle all user input validation globally

	app.useGlobalPipes(new ValidateInputPipe());

	//use globally to check auth module from request header
	// app.useGlobalGuards(new AuthModuleGuard())

	try {
		await app.listen(port, () => HttpPortLog(port));
	} catch (error) {
		console.log(error);
	}
}

bootstrap();
