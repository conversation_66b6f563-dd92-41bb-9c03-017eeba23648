import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    UseGuards,
    Req,
    Res,
} from "@nestjs/common";
import { BusinessRoleService } from "./business-role.service";
import { CreateBusinessRoleDto } from "./dto/create-business-role.dto";
import { UpdateBusinessRoleDto } from "./dto/update-business-role.dto";
import { ServiceTokenGuard } from "../guards";
import { Response } from "express";

@Controller("business-role")
export class BusinessRoleController {
    constructor(private readonly businessRoleService: BusinessRoleService) {}

    @UseGuards(ServiceTokenGuard)
    @Post("v1/create")
    async create(
        @Body() createBusinessRoleDto: CreateBusinessRoleDto,
        @Req() req,
        @Res() res: Response
    ) {
        let data = await this.businessRoleService.create(
            createBusinessRoleDto,
            req?.user
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Get("v1/data")
    async findAll(
        @Req() req,
        @Query("skip") skip: string,
        @Query("limit") limit: string,
        @Query("search") search: string,
        @Query("is_published") is_published: string,
        @Query("business_department_id") business_department_id: number,
        @Res() res: Response
    ) {
        let is_all = false;
        if ((skip == "0" && limit == "0" ) || skip  == undefined || limit == undefined) {
            is_all = true;
        }
        const skipNumber = parseInt(skip, 10) || 0;
        const limitNumber = parseInt(limit, 10) || 10;
        const isPublishedNumber = is_published !== undefined ? parseInt(is_published, 10) : undefined;
        let data = await this.businessRoleService.findAll(
            skipNumber,
            limitNumber,
            is_all,
            search,
            isPublishedNumber,
            business_department_id,
            req?.user
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Get("v1/details/:id")
    async findOne(@Param("id") id: string, @Res() res: Response) {
        let data = await this.businessRoleService.findOne(+id);
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Patch("v1/update/:id")
    async update(
        @Param("id") id: string,
        @Body() updateBusinessRoleDto: UpdateBusinessRoleDto,
        @Req() req,
        @Res() res: Response
    ) {
        let data = await this.businessRoleService.update(
            +id,
            updateBusinessRoleDto,
            req?.user
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Patch("v1/business-role-status-update/:id")
    async businessRoleStatusUpdate(
        @Req() req,
        @Param("id") id: string,
        @Body("is_published") is_published: number,
        @Res() res: Response
    ) {
        let data = await this.businessRoleService.businessRoleStatusUpdate(
            +id,
            is_published,
            req?.user
        );
        return res.status(data.statusCode).json(data);
    }

    @UseGuards(ServiceTokenGuard)
    @Delete(":id")
    async remove(@Param("id") id: string, @Res() res: Response) {
        let data = await this.businessRoleService.remove(+id);
        return res.status(data.statusCode).json(data);
    }
}
